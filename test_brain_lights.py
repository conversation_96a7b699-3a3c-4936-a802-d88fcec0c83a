#!/usr/bin/env python3
"""
Test Brain Lights Integration
============================

Test the brain's ability to detect and process light control commands.
"""

import asyncio
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_brain_lights():
    """Test the brain's light control detection and execution."""
    print("Testing brain lights integration...")
    
    try:
        import llm_brain
        
        # Test prompts that should trigger light control
        test_prompts = [
            'Use<PERSON> <PERSON> said: "Luna turn off my closet light" in channel #luna-commands',
            'Use<PERSON> said: "Turn off my closet light" in channel #luna-commands',
            '<PERSON><PERSON> said: "make the bedroom red" in channel #luna-commands',
            '<PERSON><PERSON> said: "turn on kitchen light" in channel #luna-commands'
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n--- Test {i} ---")
            print(f"Prompt: {prompt}")
            
            try:
                # Test just the brain decision part
                raw_decision = await llm_brain.get_brain_decision(prompt)
                print(f"Raw brain decision: {raw_decision}")

                # Test if it contains light control keywords
                if any(keyword in raw_decision.lower() for keyword in ['control lights', 'lights control', 'smart lights']):
                    print("✅ Brain decision contains light control keywords")
                else:
                    print("❌ Brain decision does not contain light control keywords")

                # For now, just return a mock result
                result = {'action': 'test', 'raw_decision': raw_decision}
                print(f"Brain result: {result}")
                
                action = result.get('action', 'unknown')
                if action == 'lights_control':
                    print("✅ Brain correctly identified lights control")
                    if result.get('success'):
                        print("✅ Lights command executed successfully")
                        lights_result = result.get('lights_result', {})
                        function_result = lights_result.get('result', {})
                        if 'status' in function_result:
                            print(f"✅ Light action: {function_result['status']}")
                        elif 'error' in function_result:
                            print(f"❌ Light error: {function_result['error']}")
                    else:
                        print("❌ Lights command failed")
                        lights_result = result.get('lights_result', {})
                        print(f"Failure details: {lights_result}")
                elif action == 'lights_error':
                    print("❌ Lights error occurred")
                    print(f"Error: {result.get('error', 'Unknown error')}")
                else:
                    print(f"❌ Brain did not identify lights control (action: {action})")
                    
            except Exception as e:
                print(f"❌ Error testing prompt: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during brain lights testing: {e}")
        return False

async def main():
    """Run the brain lights test."""
    print("🔍 Testing Brain Lights Integration")
    print("=" * 50)
    
    success = await test_brain_lights()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Brain lights testing completed!")
    else:
        print("❌ Brain lights testing failed!")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

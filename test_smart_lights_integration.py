#!/usr/bin/env python3
"""
Test Script for Smart Lights Integration
========================================

This script tests the complete smart lights integration with Luna,
including bulb discovery, brain processing, and command execution.
"""

import asyncio
import logging
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_bulb_discovery():
    """Test LIFX bulb discovery functionality."""
    print("\n=== Testing Bulb Discovery ===")
    
    try:
        from llm_response.smart_lights import discover_bulbs, get_available_bulbs
        
        # Test bulb discovery
        print("Discovering LIFX bulbs...")
        bulbs = discover_bulbs(force_refresh=True)
        print(f"Discovered {len(bulbs)} bulbs: {list(bulbs.keys())}")
        
        # Test available bulbs function
        available = get_available_bulbs()
        print(f"Available bulbs: {available}")
        
        return len(bulbs) > 0
        
    except ImportError as e:
        print(f"❌ Could not import smart lights module: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during bulb discovery: {e}")
        return False

async def test_light_functions():
    """Test basic light control functions."""
    print("\n=== Testing Light Control Functions ===")
    
    try:
        from llm_response.smart_lights import turn_on, turn_off, set_color, get_available_bulbs
        
        available_bulbs = get_available_bulbs()
        if not available_bulbs:
            print("⚠️ No bulbs available for testing")
            return False
        
        test_bulb = available_bulbs[0]
        print(f"Testing with bulb: {test_bulb}")
        
        # Test turn on
        print("Testing turn_on...")
        result = turn_on(test_bulb)
        print(f"Turn on result: {result}")
        
        # Wait a moment
        await asyncio.sleep(1)
        
        # Test color change
        print("Testing set_color...")
        red_color = {"r": 255, "g": 0, "b": 0}
        result = set_color(test_bulb, red_color, 50)
        print(f"Set color result: {result}")
        
        # Wait a moment
        await asyncio.sleep(2)
        
        # Test turn off
        print("Testing turn_off...")
        result = turn_off(test_bulb)
        print(f"Turn off result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during light function testing: {e}")
        return False

async def test_lights_brain():
    """Test the smart lights brain module."""
    print("\n=== Testing Smart Lights Brain ===")
    
    try:
        from llm_response.smart_lights_brain import process_lights_command
        
        test_commands = [
            "turn on closet light",
            "turn off bedroom light", 
            "make living room red",
            "set kitchen light blue at 50%",
            "dim the office light"
        ]
        
        for cmd in test_commands:
            print(f"\nTesting command: '{cmd}'")
            try:
                result = await process_lights_command(cmd)
                print(f"Result: {result}")
                
                if result.get('success'):
                    print("✅ Command processed successfully")
                else:
                    print("⚠️ Command processing failed or not applicable")
                    
            except Exception as e:
                print(f"❌ Error processing command '{cmd}': {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import smart lights brain: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during brain testing: {e}")
        return False

async def test_brain_integration():
    """Test integration with main brain system."""
    print("\n=== Testing Brain Integration ===")
    
    try:
        import llm_brain
        
        # Test light-related prompts
        test_prompts = [
            "User 'TestUser' said: 'Luna turn on my closet light'",
            "User 'TestUser' asked: 'can you turn off the bedroom light?'",
            "User 'TestUser' said: 'make the living room red please'"
        ]
        
        for prompt in test_prompts:
            print(f"\nTesting brain prompt: '{prompt}'")
            try:
                # Test brain decision
                decision = await llm_brain.get_brain_decision(prompt)
                print(f"Brain decision: {decision}")
                
                # Check if it detected lights control
                if "control lights" in decision.lower() or "lights" in decision.lower():
                    print("✅ Brain correctly identified lights command")
                else:
                    print("⚠️ Brain may not have identified lights command")
                    
            except Exception as e:
                print(f"❌ Error testing brain prompt '{prompt}': {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import llm_brain: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during brain integration testing: {e}")
        return False

async def test_command_detection():
    """Test light command detection."""
    print("\n=== Testing Command Detection ===")
    
    try:
        from llm_response.smart_lights import is_light_related_command
        
        light_commands = [
            "turn on the lights",
            "make bedroom red", 
            "dim the kitchen light",
            "turn off closet bulb",
            "brighten living room"
        ]
        
        non_light_commands = [
            "what's the weather?",
            "play some music",
            "tell me a joke",
            "what time is it?"
        ]
        
        print("Testing light-related commands:")
        for cmd in light_commands:
            is_light = is_light_related_command(cmd)
            status = "✅" if is_light else "❌"
            print(f"  {status} '{cmd}' -> {is_light}")
        
        print("\nTesting non-light commands:")
        for cmd in non_light_commands:
            is_light = is_light_related_command(cmd)
            status = "✅" if not is_light else "❌"
            print(f"  {status} '{cmd}' -> {is_light}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during command detection testing: {e}")
        return False

async def main():
    """Run all tests."""
    print("🔍 Starting Smart Lights Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Bulb Discovery", test_bulb_discovery),
        ("Command Detection", test_command_detection),
        ("Light Functions", test_light_functions),
        ("Lights Brain", test_lights_brain),
        ("Brain Integration", test_brain_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            print(f"💥 {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Smart lights integration is ready!")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

import os
# Configure Vulkan backend for multi-GPU setup
# RTX 4070 for main response model, RX 6650XT for secondary models
os.environ["GGML_BACKEND"] = "vulkan"
# Disable CUDA to force Vulkan usage
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["GGML_CUDA"] = "0"
# Set Vulkan device visibility for both GPUs
# Device 0: RTX 4070 (NVIDIA) - Main response model
# Device 1: RX 6650XT (AMD) - Decision/utility models
os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"
import logging
from openai import OpenAI
import httpx
import backoff
import asyncio
from llama_cpp import Llama
import threading
from typing import Optional

# Import constants from the config module
from .config import (
    LM_STUDIO_URL,
    LM_STUDIO_API_KEY,
    LM_STUDIO_MODEL_NAME,
    LLAMA_CPP_MODEL_PATH,
    LLAMA_CPP_N_CTX,
    LLAMA_CPP_N_THREADS,
    LLAMA_CPP_N_GPU_LAYERS,
    LLAMA_CPP_VERBOSE,
    OLLAMA_MODEL_NAME,
    KOKORO_BASE_URL
)
from .gpu_isolation import isolated_gpu_access, get_safe_llama_params, register_model

logger = logging.getLogger(__name__)

# Global variables for clients
_lm_studio_client = None
_llama_cpp_client = None
_llama_cpp_lock = threading.Lock()

def initialize_lm_studio():
    """Stubbed: LM Studio disabled; skip initialization."""
    logger.info("LM Studio initialization skipped (disabled).")
    return None

def get_lm_studio_client():
    """Returns the initialized LM Studio client instance."""
    if _lm_studio_client is None:
        logger.warning("LM Studio client accessed before initialization!")
        return initialize_lm_studio() # Attempt initialization if not done
    return _lm_studio_client

def initialize_llama_cpp():
    """Initializes the llama.cpp client with Vulkan backend using RTX 4070 (main response model)."""
    global _llama_cpp_client

    # Check if already initialized
    if _llama_cpp_client is not None:
        logger.info("llama.cpp client already initialized.")
        return _llama_cpp_client

    logger.info("Attempting to initialize llama.cpp client with Vulkan backend on RTX 4070 (Device 0)...")
    try:
        # Validate model file exists
        if not os.path.exists(LLAMA_CPP_MODEL_PATH):
            raise FileNotFoundError(f"Model file not found at: {LLAMA_CPP_MODEL_PATH}")

        # Ensure Vulkan backend is properly configured for main model
        os.environ["GGML_BACKEND"] = "vulkan"
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"
        # Don't set GGML_VK_DEVICE globally - let GPU isolation handle it

        logger.info(f"Loading main response model from: {LLAMA_CPP_MODEL_PATH}")
        logger.info(f"Context length: {LLAMA_CPP_N_CTX}")
        logger.info(f"Threads: {LLAMA_CPP_N_THREADS}")
        logger.info(f"GPU layers: {LLAMA_CPP_N_GPU_LAYERS}")
        logger.info("GPU Backend: Vulkan (RTX 4070 - Device 0)")
        logger.info(f"Environment: GGML_BACKEND={os.environ.get('GGML_BACKEND')}")
        logger.info(f"Environment: GGML_VK_VISIBLE_DEVICES={os.environ.get('GGML_VK_VISIBLE_DEVICES')}")
        logger.info("GPU device selection handled by GPU isolation system")

        # Use isolated GPU access for NVIDIA GPU (device 0)
        with isolated_gpu_access(0, "main-response"):
            # Get safe parameters for NVIDIA GPU
            safe_params = get_safe_llama_params(
                gpu_id=0,
                model_path=LLAMA_CPP_MODEL_PATH,
                n_ctx=LLAMA_CPP_N_CTX,
                n_threads=LLAMA_CPP_N_THREADS,
                n_gpu_layers=LLAMA_CPP_N_GPU_LAYERS
            )

            # Create the llama.cpp client
            _llama_cpp_client = Llama(**safe_params)

            # Register the model
            register_model(0, "main-response")
        
        logger.info(f"✅ llama.cpp client initialized successfully with Vulkan backend!")
        logger.info(f"Main response model loaded: {LLAMA_CPP_MODEL_PATH}")
        logger.info(f"Context size: {_llama_cpp_client.n_ctx()}")
        logger.info(f"GPU: RTX 4070 (Vulkan Device 0)")
        
        return _llama_cpp_client
        
    except Exception as e:
        logger.error(f"Failed to initialize llama.cpp client with Vulkan: {e}", exc_info=True)
        _llama_cpp_client = None
        return None

def get_llama_cpp_client():
    """Returns the initialized llama.cpp client instance."""
    if _llama_cpp_client is None:
        logger.warning("llama.cpp client accessed before initialization!")
        return initialize_llama_cpp() # Attempt initialization if not done
    return _llama_cpp_client

# Legacy function names for compatibility
def initialize_ollama():
    """Legacy function - now initializes llama.cpp instead of Ollama."""
    logger.info("initialize_ollama() called - redirecting to llama.cpp initialization")
    return initialize_llama_cpp()

def get_ollama_client():
    """Legacy function - now returns llama.cpp client instead of Ollama."""
    return get_llama_cpp_client()

@backoff.on_exception(
    backoff.expo,
    (httpx.TimeoutException, httpx.ConnectError, httpx.ReadTimeout),
    max_tries=3,
    jitter=backoff.full_jitter
)
def create_lm_studio_client():
    """Create and return an OpenAI-compatible client for LM Studio"""
    logger.debug(f"Creating LM Studio client for URL: {LM_STUDIO_URL}")
    return OpenAI(
        api_key=LM_STUDIO_API_KEY,
        base_url=LM_STUDIO_URL
    )

def create_kokoro_client():
    """Create and return an OpenAI-compatible client for Kokoro."""
    logger.debug(f"Creating Kokoro client for URL: {KOKORO_BASE_URL}")
    return OpenAI(
        base_url=KOKORO_BASE_URL,
        api_key="not-needed" # API key is not needed for Kokoro
    )

async def preload_ollama_model(model_name: str):
    """Legacy function - llama.cpp models are preloaded on initialization."""
    logger.info(f"preload_ollama_model() called for {model_name} - llama.cpp models are preloaded on init")
    
    try:
        # Ensure llama.cpp client is initialized
        client = get_llama_cpp_client()
        if client is not None:
            logger.info(f"✅ llama.cpp model is already loaded and ready")
            return True
        else:
            logger.warning(f"⚠️ llama.cpp client not initialized")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking llama.cpp model: {e}")
        return False

# Legacy function for compatibility  
preload_llama_cpp_model = preload_ollama_model

# --- Initialize clients upon module load (optional, or can be called explicitly) ---
# It might be better to initialize these lazily or explicitly in main.py after env vars are loaded.
# For now, we provide the creation functions.

# Example of explicit initialization call (usually done in main.py)
if __name__ == '__main__':
    logging.basicConfig(level=logging.DEBUG)
    # Load environment variables (replace with your actual loading mechanism)
    # from dotenv import load_dotenv
    # load_dotenv()
    print("Attempting to initialize LM Studio client...")
    try:
        client = initialize_lm_studio()
        print(f"LM Studio Initialized: {client is not None}")
    except Exception as e:
        print(f"LM Studio initialization failed: {e}")

    print("\nAttempting to initialize llama.cpp client...")
    try:
        llama_client = initialize_llama_cpp()
        print(f"llama.cpp Client Initialized: {llama_client is not None}")
    except Exception as e:
        print(f"llama.cpp Client initialization failed: {e}")

    print("\nAttempting to create Kokoro client...")
    try:
        k_client = create_kokoro_client()
        print(f"Kokoro Client Created: {k_client is not None}")
    except Exception as e:
        print(f"Kokoro Client Creation failed: {e}")
#!/usr/bin/env python3
"""
Simple Brain Test
================

Test the brain's ability to complete responses with increased tokens.
"""

import asyncio
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_brain():
    """Test the brain's response completion."""
    print("Testing brain with increased tokens...")
    
    try:
        import llm_brain
        
        # Test the brain decision
        test_prompt = 'User <PERSON> said: "Luna turn off my closet light" in channel #luna-commands'
        raw_decision = await llm_brain.get_brain_decision(test_prompt)
        print("Raw brain decision:", repr(raw_decision))
        
        # Check if it contains the Action: statement
        if "Action:" in raw_decision:
            print("✅ Brain completed with Action: statement")
            if "Control Lights" in raw_decision or "control lights" in raw_decision:
                print("✅ Brain correctly identified lights control")
            else:
                print("❌ Brain did not identify lights control")
        else:
            print("❌ Brain response incomplete - no Action: statement")
        
        # Check for light-related keywords
        light_keywords = ["lights", "light", "control", "smart"]
        found_keywords = [kw for kw in light_keywords if kw in raw_decision.lower()]
        print(f"Light keywords found: {found_keywords}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during brain testing: {e}")
        return False

async def main():
    """Run the brain test."""
    print("🔍 Testing Brain Response Completion")
    print("=" * 50)
    
    success = await test_brain()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Brain testing completed!")
    else:
        print("❌ Brain testing failed!")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

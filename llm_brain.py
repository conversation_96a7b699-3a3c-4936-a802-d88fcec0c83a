import os
import asyncio
import re
import threading
import time
import logging
from llama_cpp import Llama

# Import Discord for member search functionality
try:
    import discord
except ImportError:
    discord = None

# Import necessary components from llm_response package
from llm_response.commands import generate_call_message
from llm_response.config import (
    CALL_NOTIFICATION_CHANNEL_ID_STR,
    get_id_from_alias,
    get_main_name_by_id,
    GEMMA_CPP_MODEL_PATH,
    LLAMA_CPP_N_CTX,
    LLAMA_CPP_N_THREADS,
)
from llm_response.gpu_isolation import isolated_gpu_access, get_safe_llama_params, register_model

# Import smart lights brain for light control
try:
    from llm_response.smart_lights_brain import process_lights_command
except ImportError:
    logger.warning("Smart lights brain module not available")
    process_lights_command = None

# Import latency tracking
try:
    from llm_response.latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracking isn't available
    def mark_latency_timestamp(event_name):
        pass

# Load environment variables (if needed)
try:
    from dotenv import load_dotenv
    load_dotenv('local.env')
except ImportError:
    pass

# Setup logger
logger = logging.getLogger(__name__)
# Basic logging configuration (can be refined in main app setup)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Configuration (Consider moving to a dedicated config file later)
LM_STUDIO_URL = os.getenv("LM_STUDIO_API_URL", "http://127.0.0.1:1234/v1") # Default URL if not set in .env
MODEL_NAME = "gemma-3-4b-it-qat" # As specified
TEMPERATURE = 0.4 # Adjust as needed

# Cache for the system prompt
_llm_brain_system_prompt = None

# Cache for the Gemma decision model client
_gemma_cpp_client = None
_gemma_cpp_lock = threading.Lock()
_gemma_inference_lock = asyncio.Lock()  # Async lock for inference

def get_gemma_cpp_client():
    """Initialize and return a cached llama.cpp client for the Gemma decision model using RX 6650XT (Device 1)."""
    global _gemma_cpp_client
    if _gemma_cpp_client is None:
        with _gemma_cpp_lock:
            if _gemma_cpp_client is None:
                # Ensure model path exists
                if not os.path.exists(GEMMA_CPP_MODEL_PATH):
                    logger.error(f"Gemma decision model not found at: {GEMMA_CPP_MODEL_PATH}")
                    raise FileNotFoundError(f"Gemma decision model not found at: {GEMMA_CPP_MODEL_PATH}")

                logger.info("Initializing Gemma brain model with GPU isolation...")

                # Use isolated GPU access for AMD GPU (device 1)
                with isolated_gpu_access(1, "gemma-brain"):
                    # Get safe parameters for AMD GPU
                    safe_params = get_safe_llama_params(
                        gpu_id=1,
                        model_path=GEMMA_CPP_MODEL_PATH,
                        n_ctx=LLAMA_CPP_N_CTX,
                        n_threads=LLAMA_CPP_N_THREADS
                    )

                    # Create and cache the client
                    _gemma_cpp_client = Llama(**safe_params)

                    # Register the model
                    register_model(1, "gemma-brain")

                logger.info("✅ Gemma brain llama.cpp client initialized successfully!")
                logger.info(f"Brain model loaded: {GEMMA_CPP_MODEL_PATH}")
                logger.info("GPU: RX 6650XT (Device 1) - Isolated configuration")
    return _gemma_cpp_client

def _load_brain_system_prompt():
    """Loads the system prompt from llm_brain_prompt.txt"""
    global _llm_brain_system_prompt
    if _llm_brain_system_prompt is None:
        try:
            with open('llm_brain_prompt.txt', 'r', encoding='utf-8') as f:
                _llm_brain_system_prompt = f.read()
            logger.info("LLM Brain system prompt loaded successfully.")
        except FileNotFoundError:
            logger.error("llm_brain_prompt.txt not found. Using default fallback.")
            _llm_brain_system_prompt = "You are Luna's decision-making core. Decide the action: Disconnect User, DM User, Call User, or No Action."
        except Exception as e:
            logger.error(f"Error loading llm_brain_prompt.txt: {e}. Using default fallback.")
            _llm_brain_system_prompt = "You are Luna's decision-making core. Decide the action: Disconnect User, DM User, Call User, or No Action."
    return _llm_brain_system_prompt

async def get_brain_decision(prompt: str) -> str: # Converted to async
    # Prepare the system and user prompt
    system_prompt_content = _load_brain_system_prompt()
    full_prompt = f"System: {system_prompt_content}\n\nUser: {prompt}\n\nAssistant:"

    # Use cached Gemma llama.cpp client to run inference
    mark_latency_timestamp("brain_api_start")
    model = get_gemma_cpp_client()
    
    # Use async lock to prevent concurrent access to the same model instance
    async with _gemma_inference_lock:
        loop = asyncio.get_event_loop()
        def run_inference():
            return model(full_prompt, max_tokens=50, temperature=TEMPERATURE, top_p=0.9)

        result = await loop.run_in_executor(None, run_inference)
    
    mark_latency_timestamp("brain_api_end")

    # Extract decision text from llama.cpp result
    decision = result.get('choices', [{}])[0].get('text', '').strip()
    logger.info(f"LLM Brain Decision (llama.cpp): {decision}")
    return decision


# --- Call User Execution Logic (Moved from processing.py) ---
async def _execute_call_user(*, bot, sink, target_name: str, system_prompt: str, effective_text_channel):
    """
    Finds a user by name, generates a call message, and sends notifications.
    """
    logger.info(f"Brain executing call for target: {target_name}")
    target_member = None
    target_user_id = None # To store ID if found via alias
    search_method = "unknown"
    guild = sink.voice_client.guild if sink and sink.voice_client else None

    if not guild:
        logger.error("Cannot execute call: No guild context available.")
        if effective_text_channel: await effective_text_channel.send(f"Sorry, I can't call {target_name} right now (missing server context).")
        return False # Indicate failure

    target_name_lower = target_name.lower()

    # --- User Finding Logic (Prioritize Alias Lookup) ---

    # 1. Check Alias Dictionary First
    target_user_id = get_id_from_alias(target_name_lower)
    if target_user_id:
        logger.info(f"Found alias '{target_name_lower}' mapping to ID: {target_user_id}")
        # Try cache first
        target_member = guild.get_member(target_user_id)
        if target_member:
            search_method = "alias_cache"
            logger.info(f"Found member {target_member.display_name} ({target_user_id}) in cache via alias.")
        else:
            # If not in cache, try fetching by ID
            logger.info(f"Member {target_user_id} (from alias) not in cache, attempting fetch by ID...")
            try:
                target_member = await guild.fetch_member(target_user_id)
                search_method = "alias_fetch"
                logger.info(f"Successfully fetched member {target_member.display_name} ({target_user_id}) via alias ID.")
            except discord.NotFound:
                logger.warning(f"Member with ID {target_user_id} (from alias) not found in guild.")
            except discord.Forbidden:
                logger.warning(f"Bot lacks permission to fetch member by ID {target_user_id}.")
            except Exception as e:
                logger.error(f"Error fetching member by ID {target_user_id}: {e}")
                target_member = None # Ensure member is None on error

    # --- Fallback to Name Search if Alias Lookup Failed ---
    if not target_member:
        logger.info(f"No member found via alias '{target_name_lower}', falling back to name search...")

        # 2. Check current voice channel members (if applicable)
        if sink and sink.voice_client and sink.voice_client.channel:
            vc_members = sink.voice_client.channel.members
            if discord:
                target_member = discord.utils.find(lambda m: m.display_name.lower() == target_name_lower or m.name.lower() == target_name_lower, vc_members)
            else:
                # Fallback without discord.utils
                for member in vc_members:
                    if member.display_name.lower() == target_name_lower or member.name.lower() == target_name_lower:
                        target_member = member
                        break
            if target_member: search_method = "vc_current"

        # 3. Check guild members cache (display name first, then username)
        if not target_member:
            if discord:
                target_member = discord.utils.find(lambda m: m.display_name.lower() == target_name_lower, guild.members)
            else:
                # Fallback without discord.utils
                for member in guild.members:
                    if member.display_name.lower() == target_name_lower:
                        target_member = member
                        break
            if target_member: search_method = "cache_display"
        if not target_member:
            if discord:
                target_member = discord.utils.find(lambda m: m.name.lower() == target_name_lower, guild.members)
            else:
                # Fallback without discord.utils
                for member in guild.members:
                    if member.name.lower() == target_name_lower:
                        target_member = member
                        break
            if target_member: search_method = "cache_username"

        # 4. Fetch all members if still not found (last resort for names)
        if not target_member:
            logger.info(f"Target '{target_name}' not found in cache, attempting fetch all...")
            try:
                # Consider limiting fetch if guild is huge, but flatten() implies fetching all anyway
                fetched_members = await guild.fetch_members(limit=None).flatten()
                # Search fetched list by display name
                if discord:
                    target_member = discord.utils.find(lambda m: m.display_name.lower() == target_name_lower, fetched_members)
                else:
                    # Fallback without discord.utils
                    for member in fetched_members:
                        if member.display_name.lower() == target_name_lower:
                            target_member = member
                            break
                if target_member: search_method = "fetch_display"
                # Search fetched list by username if still not found
                if not target_member:
                    if discord:
                        target_member = discord.utils.find(lambda m: m.name.lower() == target_name_lower, fetched_members)
                    else:
                        # Fallback without discord.utils
                        for member in fetched_members:
                            if member.name.lower() == target_name_lower:
                                target_member = member
                                break
                    if target_member: search_method = "fetch_username"
                if target_member: logger.info(f"Found member '{target_name}' via fetch.")
            except Exception as e:
                if discord and hasattr(e, '__class__') and e.__class__.__name__ == 'Forbidden':
                    logger.warning("Failed to fetch members: Bot lacks necessary permissions (Server Members Intent likely missing).")
                else:
                    logger.error(f"Error fetching members: {e}")

    # Log final result of finding user
    if target_member: logger.info(f"Successfully identified target_member '{target_member.display_name}' (ID: {target_member.id}) using method: {search_method}")
    else:
        logger.warning(f"Failed to identify target_member for '{target_name}' after all search methods.")
        if effective_text_channel: await effective_text_channel.send(f"Sorry, I couldn't find anyone named '{target_name}' to call.")
        return False # Indicate failure

    # --- Notification Logic (Copied from processing.py) ---
    notification_channel = None
    if CALL_NOTIFICATION_CHANNEL_ID_STR:
        try:
            notification_channel_id = int(CALL_NOTIFICATION_CHANNEL_ID_STR)
            notification_channel = bot.get_channel(notification_channel_id)
            if not notification_channel: logger.warning(f"Notification channel ID {notification_channel_id} not found.")
        except ValueError: logger.error(f"Invalid CALL_NOTIFICATION_CHANNEL_ID: '{CALL_NOTIFICATION_CHANNEL_ID_STR}'. Must be an integer.")
        except Exception as e: logger.error(f"Error getting notification channel {CALL_NOTIFICATION_CHANNEL_ID_STR}: {e}")
    else: logger.warning("CALL_NOTIFICATION_CHANNEL_ID environment variable not set.")

    if not notification_channel:
         logger.warning("Cannot send call notification: Notification channel not found or configured.")
         # Optionally inform user if possible
         if effective_text_channel: await effective_text_channel.send(f"I found {target_name}, but I don't know where to send the call notification.")
         # Proceed to DM attempt? Or fail here? Let's try DM anyway.

    luna_voice_channel = sink.voice_client.channel if sink and sink.voice_client else None
    if not luna_voice_channel:
        logger.error("Cannot execute call: Luna is not in a voice channel.")
        if effective_text_channel: await effective_text_channel.send(f"Sorry, I can't call {target_name} because I'm not in a voice channel myself.")
        return False # Indicate failure

    # Generate message and send notifications
    try:
        call_body = await generate_call_message(target_name, system_prompt)
        message_to_send = f"{target_member.mention} {call_body} Join {luna_voice_channel.mention}"

        # Send to notification channel if found
        if notification_channel:
            try:
                await notification_channel.send(message_to_send)
                logger.info(f"Sent call notification for {target_name} to channel {notification_channel.name}")
            except (discord.Forbidden, discord.HTTPException) as send_error:
                 logger.error(f"Failed to send call notification to channel #{notification_channel.name}: {send_error}")
                 if effective_text_channel: await effective_text_channel.send(f"(Couldn't send notification to #{notification_channel.name}.)")

        # Attempt DM
        dm_success = False
        try:
            await target_member.send(message_to_send)
            logger.info(f"Sent call notification DM to {target_name}")
            dm_success = True
        except (discord.Forbidden, discord.HTTPException) as dm_error:
            logger.warning(f"Could not send DM to {target_name}: {dm_error}")
            if effective_text_channel: await effective_text_channel.send(f"(Couldn't send a DM to {target_name}, they might have them disabled.)")

        # Confirmation in original channel
        if effective_text_channel:
             if notification_channel or dm_success:
                  await effective_text_channel.send(f"Okay, I'm calling {target_name}.")
             else: # Neither notification channel nor DM worked
                  await effective_text_channel.send(f"I found {target_name}, but couldn't send the notification anywhere.")
                  return False # Indicate overall failure if no notification sent

        return True # Indicate success

    except Exception as e:
        logger.error(f"Unexpected error during call execution for {target_name}: {e}", exc_info=True)
        if effective_text_channel: await effective_text_channel.send(f"Sorry, something went wrong while trying to call {target_name}.")
        return False # Indicate failure


# --- Brain Request Processing & Execution ---
async def process_brain_request(*, prompt: str, bot, sink, system_prompt: str, effective_text_channel):
    """
    Gets a decision from the LLM brain, parses it, and executes the action if possible.
    Returns a dictionary describing the action taken or decided.
    """
    mark_latency_timestamp("brain_decision_start")
    
    # Now get_brain_decision is async, so we await it
    raw_decision = await get_brain_decision(prompt)
    logger.info(f"LLM Brain Raw Output: '{raw_decision}'") # Log the raw output immediately
    
    mark_latency_timestamp("brain_decision_end")

    # Basic parsing (can be made more robust)
    action_detail = {'action': 'error', 'raw_decision': raw_decision, 'details': 'Could not parse decision.'} # Default error

    # Simplified parsing approach - look for "Action: X" pattern first
    # Handle multi-word actions like "Control Lights"
    action_match = re.match(r"Action:\s*([^.\n]+)", raw_decision, re.IGNORECASE)

    if action_match:
        action_full = action_match.group(1).strip()
        action_type = action_full.split()[0].lower()  # First word for matching

        # Special handling for "Control Lights"
        if "control" in action_full.lower() and "lights" in action_full.lower():
            action_type = "control_lights"

        # --- Call User Parsing & Execution ---
        if action_type == "call":
            # Extract target name - everything after "Action: Call" until end of line or punctuation
            call_match = re.match(r"Action:\s*Call\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
            if call_match:
                target_name = call_match.group(1).strip()
                logger.info(f"Brain decided to call user: {target_name}")
                # Execute the call asynchronously
                logger.info(f"Attempting to execute call for {target_name} via _execute_call_user...")
                success = False # Default to failure
                try:
                    success = await _execute_call_user(
                        bot=bot,
                        sink=sink,
                        target_name=target_name,
                        system_prompt=system_prompt,
                        effective_text_channel=effective_text_channel
                    )
                    logger.info(f"Call execution for {target_name} completed. Success: {success}")
                except Exception as exec_err:
                    logger.error(f"Exception during _execute_call_user for {target_name}: {exec_err}", exc_info=True)
                    success = False
                action_detail = {'action': 'call_user', 'target': target_name, 'success': success, 'raw_decision': raw_decision}

        # --- DM User Parsing ---
        elif action_type == "dm":
            # Try to match specific message format first: DM <user> message "<message>"
            dm_message_match = re.match(r"Action:\s*DM\s+(.*?)\s+message\s+\"([^\"]*)\"", raw_decision, re.IGNORECASE)
            if dm_message_match:
                target_identifier = dm_message_match.group(1).strip()
                exact_message = dm_message_match.group(2).strip()
                logger.info(f"Brain decided to DM user: {target_identifier} with exact message: \"{exact_message}\"")
                action_detail = {'action': 'dm_user', 'target': target_identifier, 'exact_message': exact_message, 'success': True, 'raw_decision': raw_decision}
            else:
                # Fallback to topic format: DM <user> about "<topic>"
                dm_topic_match = re.match(r"Action:\s*DM\s+(.*?)\s+about\s+\"([^\"]*)\"", raw_decision, re.IGNORECASE)
                if dm_topic_match:
                    target_identifier = dm_topic_match.group(1).strip()
                    topic = dm_topic_match.group(2).strip()
                    logger.info(f"Brain decided to DM user: {target_identifier} about \"{topic}\"")
                    action_detail = {'action': 'dm_user', 'target': target_identifier, 'topic': topic, 'success': True, 'raw_decision': raw_decision}

        # --- No Action Parsing ---
        elif action_type == "no":
            logger.info("Brain decided no action is needed.")
            action_detail = {'action': 'no_action', 'raw_decision': raw_decision}

        # --- Disconnect User Parsing (Placeholder) ---
        elif action_type == "disconnect":
            disconnect_match = re.match(r"Action:\s*Disconnect\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
            if disconnect_match:
                target_name = disconnect_match.group(1).strip()
                logger.info(f"Brain decided to disconnect user: {target_name}")
                # TODO: Implement _execute_disconnect_user helper and call it
                action_detail = {'action': 'disconnect_user', 'target': target_name, 'success': False, 'raw_decision': raw_decision}

        # --- Smart Lights Control Parsing & Execution ---
        elif action_type == "control_lights" or (action_type == "control" and "lights" in raw_decision.lower()):
            logger.info("Brain decided to control lights")
            if process_lights_command:
                try:
                    # Extract the original user prompt from the brain prompt
                    # The brain prompt typically contains the user's original message
                    user_prompt_match = re.search(r"said:\s*['\"]([^'\"]+)['\"]", prompt, re.IGNORECASE)
                    if not user_prompt_match:
                        user_prompt_match = re.search(r"asked:\s*['\"]([^'\"]+)['\"]", prompt, re.IGNORECASE)

                    if user_prompt_match:
                        original_command = user_prompt_match.group(1)
                    else:
                        # Fallback: use the entire prompt
                        original_command = prompt

                    logger.info(f"Processing lights command: {original_command}")
                    lights_result = await process_lights_command(original_command)

                    if lights_result.get('success', False):
                        action_detail = {
                            'action': 'lights_control',
                            'success': True,
                            'lights_result': lights_result,
                            'raw_decision': raw_decision
                        }
                        logger.info(f"Lights control successful: {lights_result}")
                    else:
                        action_detail = {
                            'action': 'lights_control',
                            'success': False,
                            'lights_result': lights_result,
                            'raw_decision': raw_decision
                        }
                        logger.warning(f"Lights control failed: {lights_result}")
                except Exception as lights_error:
                    logger.error(f"Error executing lights control: {lights_error}")
                    action_detail = {
                        'action': 'lights_error',
                        'success': False,
                        'error': str(lights_error),
                        'raw_decision': raw_decision
                    }
            else:
                logger.error("Lights control requested but smart_lights_brain module not available")
                action_detail = {
                    'action': 'lights_error',
                    'success': False,
                    'error': "Smart lights module not available",
                    'raw_decision': raw_decision
                }

    # Fallback for "No Action" without the "Action:" prefix
    elif re.search(r"No Action|No further action", raw_decision, re.IGNORECASE):
        logger.info("Brain decided no action is needed (fallback pattern).")
        action_detail = {'action': 'no_action', 'raw_decision': raw_decision}

    # Fallback for lights control without proper "Action:" format
    elif re.search(r"control.*lights|lights.*control|smart.*lights", raw_decision, re.IGNORECASE):
        logger.info("Brain detected lights control (fallback pattern).")
        if process_lights_command:
            try:
                # Extract the original user prompt from the brain prompt
                user_prompt_match = re.search(r"said:\s*['\"]([^'\"]+)['\"]", prompt, re.IGNORECASE)
                if not user_prompt_match:
                    user_prompt_match = re.search(r"asked:\s*['\"]([^'\"]+)['\"]", prompt, re.IGNORECASE)

                if user_prompt_match:
                    original_command = user_prompt_match.group(1)
                else:
                    # Fallback: use the entire prompt
                    original_command = prompt

                logger.info(f"Processing lights command (fallback): {original_command}")
                lights_result = await process_lights_command(original_command)

                if lights_result.get('success', False):
                    action_detail = {
                        'action': 'lights_control',
                        'success': True,
                        'lights_result': lights_result,
                        'raw_decision': raw_decision
                    }
                    logger.info(f"Lights control successful (fallback): {lights_result}")
                else:
                    action_detail = {
                        'action': 'lights_control',
                        'success': False,
                        'lights_result': lights_result,
                        'raw_decision': raw_decision
                    }
                    logger.warning(f"Lights control failed (fallback): {lights_result}")
            except Exception as lights_error:
                logger.error(f"Error executing lights control (fallback): {lights_error}")
                action_detail = {
                    'action': 'lights_error',
                    'success': False,
                    'error': str(lights_error),
                    'raw_decision': raw_decision
                }
        else:
            logger.error("Lights control detected but smart_lights_brain module not available")
            action_detail = {
                'action': 'lights_error',
                'success': False,
                'error': "Smart lights module not available",
                'raw_decision': raw_decision
            }

    # --- Error Handling ---
    elif raw_decision.startswith("Error:"):
        logger.error(f"Brain encountered an error during decision generation: {raw_decision}")
        action_detail = {'action': 'error', 'raw_decision': raw_decision, 'details': raw_decision}

    # --- Fallback/Unknown ---
    else:
        # Only log as warning if it wasn't handled by other specific actions
        if action_detail['action'] == 'error' and action_detail['details'] == 'Could not parse decision.':
             logger.warning(f"Brain returned unparseable decision: {raw_decision}")
        # Keep default error action_detail or the one set by specific parsing logic if it failed execution

    return action_detail


# Basic Test (Phase 1, Step 4) - Keep for basic API check, but doesn't test new logic
if __name__ == "__main__":
    import asyncio

    async def run_tests():
        print("Testing LLM Brain connection and prompt loading...")
        # Ensure LM Studio is running with the 'gemma-3-4b-it' model loaded.
        test_prompt = "User 'TestUser#1234' asked: 'Luna, can you disconnect Bob#5678 from the voice channel?'"
        decision = await get_brain_decision(test_prompt)
        print(f"Test Prompt: {test_prompt}")
        print(f"Received Decision: {decision}")

        print("\nTesting with a different prompt...")
        test_prompt_2 = "User 'Admin#0001' said: 'DM Alice#1111 and tell her the meeting is moved to 3 PM.'"
        decision_2 = await get_brain_decision(test_prompt_2)
        print(f"Test Prompt: {test_prompt_2}")
        print(f"Received Decision: {decision_2}")

    # Run the async test function
    asyncio.run(run_tests())
#!/usr/bin/env python3
"""
Final Integration Test
=====================

Test the complete integration with all fixes applied:
1. Brain parsing fix (no call conflicts)
2. GPU assignments working correctly
3. Lights control working end-to-end
"""

import asyncio
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_gpu_assignments():
    """Test that GPU assignments are working correctly."""
    print("Testing GPU assignments...")
    
    try:
        # Test main response model (should use GPU 0)
        import llm_response
        main_client = llm_response.initialize_ollama()  # This calls initialize_llama_cpp
        if main_client:
            print("✅ Main response model initialized (should be on RTX 4070 - GPU 0)")
        else:
            print("❌ Main response model failed to initialize")
            return False
        
        # Test brain model (should use GPU 1)
        import llm_brain
        brain_client = llm_brain.get_gemma_cpp_client()
        if brain_client:
            print("✅ Brain model initialized (should be on RX 6650XT - GPU 1)")
        else:
            print("❌ Brain model failed to initialize")
            return False
        
        # Test lights brain model (should use GPU 1)
        from llm_response.smart_lights_brain import get_lights_brain_decision
        lights_response = await get_lights_brain_decision("turn on test light")
        if lights_response:
            print("✅ Lights brain model working (should be on RX 6650XT - GPU 1)")
        else:
            print("❌ Lights brain model failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GPU assignments: {e}")
        return False

async def test_brain_lights_integration():
    """Test the complete brain to lights integration."""
    print("Testing brain to lights integration...")
    
    try:
        import llm_brain
        from llm_response.smart_lights_brain import process_lights_command
        
        # Test brain decision
        test_prompt = 'User Gavin said: "Luna turn off my closet light" in channel #luna-commands'
        raw_decision = await llm_brain.get_brain_decision(test_prompt)
        
        print(f"Brain decision: {raw_decision[:100]}...")
        
        # Check for correct action
        if "Action: Control Lights" in raw_decision:
            print("✅ Brain correctly identified lights control")
            
            # Test lights execution
            lights_result = await process_lights_command("turn off closet light")
            if lights_result.get('success'):
                print("✅ Lights command executed successfully")
                print(f"Result: {lights_result.get('result', {}).get('status', 'Unknown')}")
                
                # Clean up - turn it back on
                cleanup_result = await process_lights_command("turn on closet light")
                if cleanup_result.get('success'):
                    print("✅ Cleanup successful")
                
                return True
            else:
                print("❌ Lights command failed")
                return False
        else:
            print("❌ Brain did not identify lights control correctly")
            return False
        
    except Exception as e:
        print(f"❌ Error testing brain lights integration: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all integration tests."""
    print("🔍 Final Integration Test")
    print("=" * 50)
    
    tests = [
        ("GPU Assignments", test_gpu_assignments),
        ("Brain Lights Integration", test_brain_lights_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            print(f"💥 {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 FINAL INTEGRATION TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes working perfectly!")
        print("💡 Luna is ready for Discord lights control!")
        print("🚀 GPU assignments optimized for performance!")
    else:
        print("⚠️ Some issues remain that need attention.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

import discord
from discord.ext import commands
import asyncio
import os
import logging
import sqlite3
import time  # Add this import for timing
import torch  # Add this import for CUDA warmup
import tempfile  # <-- Add this import for video analysis
import shutil
from datetime import datetime
import numpy as np

# IMPORTANT: Load environment variables FIRST before any other imports that might depend on them
from load_environment import load_env_vars
load_env_vars()

from speech_to_text import transcribe_audio, WHISPER_MODEL
from discord_sink import DiscordSink
from text_to_speech import synthesize_speech, close_tts_engine, prewarm_tts_engine
from llm_brain import get_brain_decision # Import the brain function
# Import alias lookup functions
import re 
from llm_response.config import get_id_from_alias, get_main_name_from_alias, is_minecraft_mode_enabled
# No need to import VertexAI modules anymore
import llm_response # Import the module directly
from llm_response.auto_screenshot import auto_screenshot_manager
# Set global llama.cpp context length (legacy environment variable)
os.environ["OLLAMA_CONTEXT_LENGTH"] = "512"  # Legacy - llama.cpp context is set in config.py
import sys
# =========================================================================
# EXPLICITLY CONFIGURE LOGGING FOR THE ENTIRE APPLICATION
# This guarantees our logging settings are used, not a library's.
# =========================================================================
logging.basicConfig(
    level=logging.INFO,  # Set the minimum level of logs to capture (e.g., INFO, DEBUG)
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.FileHandler("bot.log", encoding="utf-8"),      # Optional: Log to a file
        logging.StreamHandler(sys.stdout)  # Force logs to also print to the console
    ],
    force=True # This is the key: it rips out any config set by libraries and applies this one.
)

# You can create a logger specific to this file
log = logging.getLogger(__name__)

log.info("Logging configured successfully. Starting bot...")
# =========================================================================

# --- Setup Logging ---
logger = logging.getLogger(__name__)

# Initialize llama.cpp for response generation (legacy function name)
llm_response.initialize_ollama()  # llama.cpp initialization; LM Studio disabled
# Preload Gemma decision model to warm up Vulkan and cache the client
try:
    from llm_brain import get_gemma_cpp_client
    get_gemma_cpp_client()
    logger.info("Preloaded Gemma decision model for faster decisions.")
except Exception as e:
    logger.error(f"Failed to preload Gemma decision model: {e}")

# --- Environment Variables ---
DISCORD_BOT_TOKEN = os.environ.get("DISCORD_BOT_TOKEN")
GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY")
KOKORO_FASTAPI_URL = os.environ.get("KOKORO_FASTAPI_URL")
KOKORO_VOICE = os.environ.get("KOKORO_VOICE")

if DISCORD_BOT_TOKEN is None:
    raise ValueError("DISCORD_BOT_TOKEN environment variable not set.")
if GOOGLE_API_KEY is None:
    raise ValueError("GOOGLE_API_KEY environment variable not set.")

# --- GPU Warmup Function (Vulkan Backend) ---
def warmup_gpu():
    logger.info("Warming up GPU backends (Vulkan)...")
    try:
        # Check if PyTorch can detect any GPUs (for general GPU warmup)
        if torch.cuda.is_available():
            start_time = time.monotonic()
            # Perform a small operation for PyTorch GPU warmup
            a = torch.randn(10, 10, device='cuda')
            b = torch.randn(10, 10, device='cuda')
            c = a @ b
            torch.cuda.synchronize()
            end_time = time.monotonic()
            logger.info(f"PyTorch GPU warmup completed in {end_time - start_time:.4f}s")
        
        # Vulkan backend warmup is handled by llama.cpp initialization
        logger.info("Vulkan backend warmup will occur during model loading")
        logger.info("RTX 4070 (Device 0): Main response model")
        logger.info("RX 6650XT (Device 1): Decision/utility models")
        
    except Exception as e:
        logger.error(f"GPU warmup failed: {e}")

# --- Luna-Tune Priority Warmup Function ---
def warmup_luna_tune_model():
    """Prioritized warmup for luna-tune:latest model with forced 100% GPU offloading"""
    logger.info("🚀 PRIORITY: Warming up luna-tune:latest model with 100% GPU offloading...")
    try:
        if not torch.cuda.is_available():
            logger.warning("CUDA not available, cannot force GPU offloading")
            return
        
        start_time = time.monotonic()
        import requests
        import json
        
        # Clear any existing models from VRAM first to ensure luna-tune gets priority
        try:
            requests.post("http://localhost:11434/api/generate", 
                         json={"model": "", "keep_alive": "0"}, timeout=5)
            logger.info("📦 Cleared existing models from VRAM")
        except:
            pass
        
        # Force load luna-tune:latest with maximum GPU layers and minimal context to ensure 100% GPU
        payload = {
            "model": "luna-tune:latest",
            "prompt": "warmup",
            "stream": False,
            "options": {
                "num_predict": 1,
                "temperature": 0.1,
                "num_gpu": 9999,  # Force maximum GPU layers
                "num_ctx": 512,   # Minimal context to save VRAM for model layers
                "keep_alive": "30m"  # Keep in memory
            }
        }
        
        logger.info("🔥 Loading luna-tune:latest with forced 100% GPU offloading...")
        response = requests.post("http://localhost:11434/api/generate", 
                               json=payload, timeout=30)
        
        if response.status_code == 200:
            torch.cuda.synchronize()
            end_time = time.monotonic()
            logger.info(f"✅ luna-tune:latest warmup completed in {end_time - start_time:.4f}s")
            logger.info("🎯 Model prioritized and loaded with maximum GPU allocation")
        else:
            logger.warning(f"⚠️ luna-tune warmup response: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ luna-tune warmup failed: {e}")

# --- Transcription Warmup Function ---
def warmup_transcription_model():
    logger.info("Warming up transcription model (faster-whisper)...")
    try:
        if torch.cuda.is_available(): # Only run if CUDA is used by the model
            start_time = time.monotonic()
            # Create a tiny silent audio sample (e.g., 0.1 seconds of zeros)
            # Needs to be float32, 16kHz mono
            dummy_audio = np.zeros(int(16000 * 0.1), dtype=np.float32)
            # Run a dummy transcription
            _ = list(WHISPER_MODEL.transcribe(dummy_audio, beam_size=1)[0]) # Consume the generator
            torch.cuda.synchronize() # Ensure it finishes
            end_time = time.monotonic()
            logger.info(f"Transcription model warmup completed in {end_time - start_time:.4f}s")
        else:
            logger.info("Transcription model warmup skipped (CUDA not available or model not on CUDA).")
    except Exception as e:
        logger.error(f"Transcription model warmup failed: {e}")

# --- Intents ---
intents = discord.Intents.default()
intents.message_content = True # Explicitly enable message content
intents.voice_states = True    # Explicitly enable voice states
intents.members = True         # Explicitly enable server members intent

bot = commands.Bot(command_prefix='!', intents=intents)

# Add a flag to block message processing until memory is ready
bot.memory_ready = asyncio.Event()

# Dictionary to store active text chat sessions {channel_id: ChatSession}
text_chat_sessions = {}

# Attach the chat sessions dictionary to the bot for global access
bot.chat_sessions = {}

# --- Database Path ---
DB_PATH = "conversation_logs.db" # Define DB Path globally

# Database logging is now handled by llm_response.log_message_to_db
# --- System Prompt Setup ---
try:
    with open('luna_prompt.txt', 'r') as f:
        SYSTEM_PROMPT = f.read()
except Exception as e:
    logger.error(f"Error loading system prompt: {e}")
    SYSTEM_PROMPT = ""

# Add at the top:
from collections import deque

# Maintain a rolling history of Minecraft bot events
mc_event_history = deque(maxlen=20)

async def auto_join_user_voice_channel():
    """Automatically join the voice channel if the specified user is in one"""
    TARGET_USER_ID = 921637353364287489  # Your Discord ID
    
    try:
        logger.info(f"🔍 Checking if user {TARGET_USER_ID} is in a voice channel...")
        
        # Search through all guilds the bot is in
        for guild in bot.guilds:
            # Get the member object for the target user
            member = guild.get_member(TARGET_USER_ID)
            if member and member.voice and member.voice.channel:
                voice_channel = member.voice.channel
                logger.info(f"🎯 Found user in voice channel: {voice_channel.name} in {guild.name}")
                
                # Check if bot is already in this voice channel
                if guild.voice_client and guild.voice_client.channel == voice_channel:
                    logger.info("✅ Bot is already in the user's voice channel")
                    return
                
                # Disconnect from any other voice channel in this guild first
                if guild.voice_client:
                    logger.info("🔄 Disconnecting from current voice channel...")
                    await guild.voice_client.disconnect()
                
                try:
                    # Check bot permissions before attempting connection
                    bot_member = guild.get_member(bot.user.id)
                    if not voice_channel.permissions_for(bot_member).connect:
                        logger.error(f"❌ Bot lacks CONNECT permission for voice channel: {voice_channel.name}")
                        return
                    if not voice_channel.permissions_for(bot_member).speak:
                        logger.warning(f"⚠️ Bot lacks SPEAK permission for voice channel: {voice_channel.name}")
                    
                    logger.info(f"🔗 Attempting to connect to voice channel: {voice_channel.name} (ID: {voice_channel.id})")
                    logger.info(f"🌐 Voice region: {guild.region if hasattr(guild, 'region') else 'Unknown'}")
                    
                    # Connect to the voice channel with timeout
                    vc = await asyncio.wait_for(voice_channel.connect(), timeout=15.0)
                    logger.info("🔗 Voice connection established, verifying...")
                    
                    # Verify the connection is actually established
                    if not vc.is_connected():
                        logger.error("❌ Voice connection failed - not connected after join attempt")
                        await vc.disconnect()
                        return
                    
                    logger.info(f"✅ Successfully joined voice channel: {voice_channel.name}")
                    logger.info(f"🎤 Voice client endpoint: {vc.endpoint if hasattr(vc, 'endpoint') else 'Unknown'}")
                    logger.info("🔧 Starting DiscordSink setup...")
                    
                    # Set up the DiscordSink like in the join command
                    # Find a text channel to use (preferably one the user can see)
                    logger.info("🔍 Looking for suitable text channel...")
                    text_channel = None
                    logger.info(f"📋 Guild has {len(guild.text_channels)} text channels")
                    
                    for channel in guild.text_channels:
                        if channel.permissions_for(member).view_channel:
                            text_channel = channel
                            logger.info(f"✅ Found suitable text channel: {channel.name}")
                            break
                    
                    if not text_channel:
                        # Fallback to the first text channel the bot can see
                        logger.info("⚠️ No user-visible text channel found, trying fallback...")
                        if guild.text_channels:
                            text_channel = guild.text_channels[0]
                            logger.info(f"📝 Using fallback text channel: {text_channel.name}")
                        else:
                            text_channel = None
                            logger.error("❌ No text channels found in guild!")
                    
                    if text_channel:
                        # Initialize the DiscordSink
                        logger.info("🔧 Initializing DiscordSink...")
                        model_client = llm_response.get_llama_cpp_client()
                        if model_client:
                            logger.info("✅ Model client ready")
                            # Only create mc_bridge if enabled
                            local_mc_bridge = None
                            if is_minecraft_mode_enabled():
                                logger.info("🎮 Minecraft mode enabled, setting up bridge...")
                                from luna_mc_bridge import LunaMCBridge
                                local_mc_bridge = LunaMCBridge(agent_name="lunapotato")
                                try:
                                    local_mc_bridge.connect()
                                    local_mc_bridge.start_background_listener()
                                    logger.info("🎮 Connected to MindServer for Minecraft integration")
                                except Exception as e:
                                    logger.warning(f"⚠️ Failed to connect to MindServer: {e}")
                                    local_mc_bridge = None
                            else:
                                logger.info("🎮 Minecraft mode disabled")
                            
                            logger.info("🏗️ Creating DiscordSink instance...")
                            sink = DiscordSink(text_channel, bot, vc, model_client, mc_bridge=local_mc_bridge)
                            logger.info("📝 Setting system prompt...")
                            sink.system_prompt = SYSTEM_PROMPT
                            logger.info("🎙️ Starting voice recording...")
                            vc.start_recording(sink, lambda *args: None)
                            vc.sink = sink
                            logger.info("⏰ Creating silence check task...")
                            bot.loop.create_task(sink.check_for_silence())
                            
                            logger.info("📸 Activating auto-screenshot...")
                            # Activate auto-screenshot for this voice channel
                            auto_screenshot_manager.add_active_channel(voice_channel.id)
                            
                            logger.info("🎤 Voice recording and processing initialized")
                        else:
                            logger.error("❌ llama.cpp client not ready for auto-join")
                            await vc.disconnect()
                            return
                    else:
                        logger.warning("⚠️ No suitable text channel found for DiscordSink")
                        await vc.disconnect()
                        return
                    
                    return  # Successfully joined, exit the function
                    
                except asyncio.TimeoutError:
                    logger.error("❌ Voice connection timed out after 15 seconds")
                    logger.error("🔍 This usually indicates network/firewall issues or Discord voice server problems")
                    logger.error("💡 Try: 1) Check bot permissions 2) Try different voice region 3) Restart Discord")
                    # Clean up any partial connection
                    if guild.voice_client:
                        await guild.voice_client.disconnect()
                    return
                except discord.ClientException as e:
                    logger.error(f"❌ Error connecting to voice channel: {e}")
                    # Clean up any partial connection
                    if guild.voice_client:
                        await guild.voice_client.disconnect()
                    return
                except Exception as e:
                    import traceback
                    logger.error(f"❌ Unexpected error during voice channel setup: {e}")
                    logger.error(f"📋 Full traceback:\n{traceback.format_exc()}")
                    # Clean up any partial connection
                    if guild.voice_client:
                        await guild.voice_client.disconnect()
                    return
        
        logger.info(f"ℹ️ User {TARGET_USER_ID} is not in any voice channel")
        
    except Exception as e:
        logger.error(f"❌ Error in auto_join_user_voice_channel: {e}")

@bot.event
async def on_ready():
    logger.info(f'Logged in as {bot.user.name} ({bot.user.id})')
    logger.info("Memory system disabled - no initialization needed")
    # Memory system disabled - set to None
    bot.memory_client = None
    logger.info("✅ Bot ready without memory system")
    
    # Initialize VTube Studio integration if enabled
    try:
        import vtube_studio_client
        vts_connected = await vtube_studio_client.connect_vts()
        if vts_connected:
            logger.info("✅ VTube Studio integration initialized successfully")
        else:
            logger.info("ℹ️ VTube Studio integration disabled or connection failed")
    except Exception as e:
        logger.error(f"❌ Error initializing VTube Studio integration: {e}")
    
    bot.memory_ready.set()  # Set the flag when memory is ready
    
    # PRIORITY: Ensure luna-tune:latest is still optimally loaded after memory init
    logger.info("🎯 Re-optimizing luna-tune:latest after memory initialization...")
    try:
        import requests
        # Refresh luna-tune:latest to ensure it still has optimal GPU allocation
        payload = {
            "model": "luna-tune:latest",
            "prompt": "ready",
            "stream": False,
            "options": {
                "num_predict": 1,
                "temperature": 0.1,
                "num_gpu": 9999,  # Force maximum GPU layers again
                "num_ctx": 512,   # Keep minimal context
                "keep_alive": "30m"
            }
        }
        requests.post("http://localhost:11434/api/generate", json=payload, timeout=10)
        logger.info("✅ luna-tune:latest re-optimized for maximum GPU usage")
    except Exception as e:
        logger.warning(f"⚠️ luna-tune re-optimization failed: {e}")
    
    # Ensure llama.cpp model is ready (models are preloaded on initialization)
    logger.info("🔥 Ensuring llama.cpp model is ready for optimal TTFT...")
    try:
        from llm_response.initialization import preload_ollama_model  # Legacy function name
        from llm_response.config import OLLAMA_MODEL_NAME
        
        # Check if model is loaded (llama.cpp models are loaded on init)
        preload_success = await preload_ollama_model(OLLAMA_MODEL_NAME)
        if preload_success:
            logger.info(f"✅ llama.cpp model {OLLAMA_MODEL_NAME} is ready")
        else:
            logger.warning(f"⚠️ llama.cpp model {OLLAMA_MODEL_NAME} may not be ready - checking initialization")
            
    except Exception as e:
        logger.error(f"❌ Error checking llama.cpp model: {e}")
    
    # Initialize auto-screenshot system
    try:
        auto_screenshot_manager.set_bot(bot)
        await auto_screenshot_manager.start()
        logger.info("✅ Auto-screenshot system initialized")
    except Exception as e:
        logger.error(f"❌ Error initializing auto-screenshot system: {e}")
    
    logger.info("🚀 Bot initialization complete - all systems ready")
    
    # Auto-join voice channel if user is in one (with retry)
    try:
        await auto_join_user_voice_channel()
    except Exception as e:
        logger.error(f"❌ Auto-join failed: {e}")
        logger.info("ℹ️ You can still use !join manually when ready")

@bot.command()
async def join(ctx):
    if ctx.author.voice:
        voice_channel = ctx.author.voice.channel
        text_channel = ctx.channel
        
        # Check if bot is already connected and actually working
        if ctx.guild.voice_client:
            if ctx.guild.voice_client.is_connected() and ctx.guild.voice_client.channel == voice_channel:
                await ctx.send("Already connected to your voice channel!")
                return
            else:
                # Bot thinks it's connected but isn't, or is in wrong channel - disconnect first
                logger.info("🔄 Cleaning up existing voice connection...")
                await ctx.guild.voice_client.disconnect()
        
        try:
            # Check bot permissions before attempting connection
            bot_member = ctx.guild.get_member(bot.user.id)
            if not voice_channel.permissions_for(bot_member).connect:
                await ctx.send(f"❌ I don't have permission to connect to {voice_channel.name}")
                return
            if not voice_channel.permissions_for(bot_member).speak:
                await ctx.send(f"⚠️ Warning: I don't have permission to speak in {voice_channel.name}")
            
            await ctx.send(f"🔗 Connecting to {voice_channel.name}...")
            vc = await asyncio.wait_for(voice_channel.connect(), timeout=15.0)
            
            # Verify the connection is actually established
            if not vc.is_connected():
                logger.error("❌ Voice connection failed - not connected after join attempt")
                await ctx.send("Failed to connect to voice channel.")
                await vc.disconnect()
                return
            
            model_client = llm_response.get_llama_cpp_client()
            if not model_client:
                logger.error("llama.cpp client not initialized before !join command.")
                await ctx.send("Error: Language model is not ready.")
                await vc.disconnect()
                return
            # Only create mc_bridge if enabled
            local_mc_bridge = None
            if is_minecraft_mode_enabled():
                from luna_mc_bridge import LunaMCBridge
                local_mc_bridge = LunaMCBridge(agent_name="lunapotato")
                try:
                    local_mc_bridge.connect()
                    local_mc_bridge.start_background_listener()
                    print("[Luna] Connected to MindServer for Minecraft integration.")
                    logger.info(f"[DEBUG] mc_bridge initialized: {local_mc_bridge}")
                except Exception as e:
                    print(f"[Luna] Failed to connect to MindServer: {e}")
                    local_mc_bridge = None
            sink = DiscordSink(text_channel, bot, vc, model_client, mc_bridge=local_mc_bridge)
            sink.system_prompt = SYSTEM_PROMPT
            vc.start_recording(sink, lambda *args: None)
            vc.sink = sink
            logger.info(f"[DEBUG] Attached DiscordSink to voice client: {vc.sink}")
            bot.loop.create_task(sink.check_for_silence())
            
            # Activate auto-screenshot for this voice channel
            auto_screenshot_manager.add_active_channel(voice_channel.id)
        except asyncio.TimeoutError:
            await ctx.send("❌ Connection timed out after 15 seconds.\n"
                          "🔍 This usually indicates network/firewall issues or Discord voice server problems.\n"
                          "💡 Try: 1) Check voice channel region 2) Try again in a few minutes 3) Restart Discord")
            # Clean up any partial connection
            if ctx.guild.voice_client:
                await ctx.guild.voice_client.disconnect()
        except discord.ClientException as e:
            await ctx.send("Error connecting to voice: " + str(e))
            # Clean up any partial connection
            if ctx.guild.voice_client:
                await ctx.guild.voice_client.disconnect()
    else:
        await ctx.send("You are not in a voice channel.")

@bot.command()
async def voicedebug(ctx):
    """Debug voice connection issues"""
    if not ctx.author.voice:
        await ctx.send("❌ You need to be in a voice channel to debug.")
        return
    
    voice_channel = ctx.author.voice.channel
    guild = ctx.guild
    bot_member = guild.get_member(bot.user.id)
    
    embed = discord.Embed(title="🔍 Voice Connection Debug", color=0x00ff00)
    
    # Check permissions
    perms = voice_channel.permissions_for(bot_member)
    embed.add_field(
        name="🔐 Permissions",
        value=f"Connect: {'✅' if perms.connect else '❌'}\n"
              f"Speak: {'✅' if perms.speak else '❌'}\n"
              f"Use Voice Activity: {'✅' if perms.use_voice_activation else '❌'}",
        inline=True
    )
    
    # Channel info
    embed.add_field(
        name="📢 Channel Info",
        value=f"Name: {voice_channel.name}\n"
              f"ID: {voice_channel.id}\n"
              f"User limit: {voice_channel.user_limit or 'None'}\n"
              f"Region: {guild.region if hasattr(guild, 'region') else 'Auto'}",
        inline=True
    )
    
    # Connection status
    vc_status = "❌ Not connected"
    if guild.voice_client:
        if guild.voice_client.is_connected():
            vc_status = f"✅ Connected to {guild.voice_client.channel.name}"
        else:
            vc_status = "⚠️ Partial connection (broken)"
    
    embed.add_field(
        name="🔗 Connection Status",
        value=vc_status,
        inline=False
    )
    
    await ctx.send(embed=embed)

@bot.command()
async def leave(ctx):
    if ctx.guild.voice_client:
        # Get the voice channel ID before disconnecting
        voice_channel_id = ctx.guild.voice_client.channel.id
        
        if ctx.guild.voice_client.is_playing():
            ctx.guild.voice_client.stop()
        
        # Only stop recording if we're actually recording
        try:
            ctx.guild.voice_client.stop_recording()
        except Exception as e:
            logger.warning(f"Could not stop recording (may not be active): {e}")
        
        await ctx.guild.voice_client.disconnect()
        
        # Deactivate auto-screenshot for this voice channel
        auto_screenshot_manager.remove_active_channel(voice_channel_id)
    else:
        await ctx.send("Not in a voice channel.")

@bot.command()
async def volume(ctx, user_mention=None, multiplier: float = None):
    """
    Manage per-user volume settings for speech-to-text.
    Usage:
    - !volume - Show all volume settings
    - !volume @user - Show volume setting for specific user
    - !volume @user 1.5 - Set volume multiplier for user (1.5 = 50% louder)
    """
    from llm_response.config import (
        get_volume_multiplier_by_id,
        set_volume_multiplier_by_id,
        list_volume_settings,
        get_main_name_by_id
    )

    # If no arguments, show all volume settings
    if user_mention is None and multiplier is None:
        volume_settings = list_volume_settings()
        if not volume_settings:
            await ctx.send("No volume settings configured.")
            return

        embed = discord.Embed(title="🔊 User Volume Settings", color=0x00ff00)
        for user_id, settings in volume_settings.items():
            name = settings["main_name"]
            vol = settings["volume_multiplier"]
            status = "🔊 Boosted" if vol > 1.0 else "🔉 Reduced" if vol < 1.0 else "🔈 Default"
            embed.add_field(
                name=f"{name} ({user_id})",
                value=f"{vol:.1f}x {status}",
                inline=True
            )
        await ctx.send(embed=embed)
        return

    # Parse user mention
    if user_mention is None:
        await ctx.send("Please specify a user. Usage: `!volume @user [multiplier]`")
        return

    # Extract user ID from mention
    user_id = None
    if user_mention.startswith('<@') and user_mention.endswith('>'):
        # Discord mention format
        user_id_str = user_mention[2:-1]
        if user_id_str.startswith('!'):
            user_id_str = user_id_str[1:]  # Remove nickname indicator
        try:
            user_id = int(user_id_str)
        except ValueError:
            await ctx.send("Invalid user mention format.")
            return
    else:
        await ctx.send("Please use a proper user mention (@user).")
        return

    # If no multiplier specified, show current setting
    if multiplier is None:
        current_vol = get_volume_multiplier_by_id(user_id)
        user_name = get_main_name_by_id(user_id) or f"User {user_id}"
        status = "🔊 Boosted" if current_vol > 1.0 else "🔉 Reduced" if current_vol < 1.0 else "🔈 Default"
        await ctx.send(f"**{user_name}** volume: {current_vol:.1f}x {status}")
        return

    # Validate multiplier range
    if multiplier < 0.1 or multiplier > 5.0:
        await ctx.send("Volume multiplier must be between 0.1 and 5.0")
        return

    # Set the volume multiplier
    try:
        logger.info(f"Setting volume for user {user_id} to {multiplier:.1f}x")
        success = set_volume_multiplier_by_id(user_id, multiplier)
        if success:
            user_name = get_main_name_by_id(user_id) or f"User {user_id}"
            status = "🔊 Boosted" if multiplier > 1.0 else "🔉 Reduced" if multiplier < 1.0 else "🔈 Default"

            # Verify the setting was applied
            new_vol = get_volume_multiplier_by_id(user_id)
            logger.info(f"Volume set successfully. Verification: {new_vol:.1f}x")

            await ctx.send(f"✅ Set **{user_name}** volume to {multiplier:.1f}x {status}\n"
                          f"Config file has been updated and will persist across restarts.")
        else:
            await ctx.send(f"❌ User {user_id} not found in user profiles. Please add them to the config first.")
    except Exception as e:
        logger.error(f"Error setting volume for user {user_id}: {e}", exc_info=True)
        await ctx.send(f"❌ Error setting volume: {str(e)}")

@bot.command()
async def dbthreshold(ctx, threshold: float = None):
    """
    Manage the dB threshold for audio filtering.
    Usage:
    - !dbthreshold - Show current threshold
    - !dbthreshold -35 - Set threshold to -35 dB
    """
    from speech_to_text import get_db_threshold, set_db_threshold

    if threshold is None:
        current_threshold = get_db_threshold()
        await ctx.send(f"Current dB threshold: **{current_threshold} dB**\n"
                      f"Audio below this level will be filtered out as background noise.")
        return

    # Validate threshold range
    if threshold < -80 or threshold > 0:
        await ctx.send("dB threshold must be between -80 and 0 dB")
        return

    set_db_threshold(threshold)
    await ctx.send(f"✅ Set dB threshold to **{threshold} dB**\n"
                  f"Audio below this level will now be filtered out.")

@bot.command()
async def link_text(ctx):
    """Link the current text channel with Luna's voice chat"""
    if not ctx.guild.voice_client:
        await ctx.send("I'm not in a voice channel yet. Use !join first.")
        return

    sink = None
    for vc in bot.voice_clients:
        if vc.guild == ctx.guild and hasattr(vc, 'sink'):
            sink = vc.sink
            break

    if sink:
        # Handle different channel types (DMChannel doesn't have a name attribute)
        old_channel = sink.text_channel.name if sink.text_channel and hasattr(sink.text_channel, 'name') else ("DM" if sink.text_channel else "none")
        sink.text_channel = ctx.channel
        await ctx.send(f"✅ Linked text channel changed from #{old_channel} to #{ctx.channel.name}")
    else:
        await ctx.send("I couldn't find my voice connection. Try using !join first.")

@bot.command()
async def autoscreenshot(ctx):
    """Check auto-screenshot status and recent observations"""
    from llm_response.config import AUTO_SCREENSHOT_ENABLED, AUTO_SCREENSHOT_INTERVAL
    import time
    
    if not AUTO_SCREENSHOT_ENABLED:
        await ctx.send("📸 Auto-screenshot is disabled in config")
        return
    
    status = "🟢 Active" if auto_screenshot_manager.is_active() else "🔴 Inactive"
    active_channels = len(auto_screenshot_manager.active_channels)
    
    # Get recent observations
    recent_obs = auto_screenshot_manager.get_recent_observations(limit=3)
    
    embed = discord.Embed(
        title="📸 Auto-Screenshot Status", 
        color=0x00ff00 if auto_screenshot_manager.is_active() else 0xff0000
    )
    
    embed.add_field(name="Status", value=status, inline=True)
    embed.add_field(name="Interval", value=f"{AUTO_SCREENSHOT_INTERVAL}s", inline=True)
    embed.add_field(name="Active Channels", value=str(active_channels), inline=True)
    
    if recent_obs:
        obs_text = ""
        for obs in recent_obs:
            obs_time = obs.get("timestamp", 0)
            time_ago = int(time.time() - obs_time)
            if time_ago < 60:
                time_str = f"{time_ago}s ago"
            else:
                time_str = f"{time_ago//60}m ago"
            content = obs.get("content", "")[:60] + "..." if len(obs.get("content", "")) > 60 else obs.get("content", "")
            obs_text += f"• ({time_str}) {content}\n"
        
        embed.add_field(name="Recent Observations", value=obs_text or "None", inline=False)
    else:
        embed.add_field(name="Recent Observations", value="None", inline=False)
    
    await ctx.send(embed=embed)

@bot.event
async def on_command_error(ctx, error):
    logger.error(f"Command error: {error}")
    await ctx.send(f"Error: {error}")


@bot.event
async def on_message(message):
    await bot.memory_ready.wait()  # Block until memory is ready
    # Skip messages from the bot itself
    if message.author == bot.user:
        return

    # Process commands first (like !join, !leave)
    await bot.process_commands(message)

    # Check if the message was handled by process_commands
    ctx = await bot.get_context(message)
    if ctx.command is not None:
        # It was a command, so we don't need to process it as a regular message
        return

    # --- Special handling for DMs ---
    if isinstance(message.channel, discord.DMChannel):
        logger.info(f"Processing DM from {message.author.display_name} ({message.author.id}): {message.content[:50]}...")

        # Get or create DM chat session for statefulness
        dm_session_key = f"dm_{message.author.id}"
        dm_session = bot.chat_sessions.get(dm_session_key)

        if dm_session is None:
            logger.info(f"Creating new DM chat session for user {message.author.id}")
            dm_session = {
                "id": f"dm_{message.author.id}_{int(time.time())}",
                                    "messages": []  # No system prompt needed - using llama.cpp with built-in system prompt
            }
            bot.chat_sessions[dm_session_key] = dm_session

        # Log the DM message to database
        try:
            llm_response.log_message_to_db(
                user_id=message.author.id,
                role="user",
                content=message.content,
                timestamp=message.created_at.timestamp(),
                channel_type="dm",
                channel_id=message.channel.id
            )
        except Exception as log_err:
            logger.error(f"Error logging DM message: {log_err}", exc_info=True)

        # Process the DM using llama.cpp with stateful conversation
        try:
            await llm_response.process_user_message(
                bot=bot,
                text=message.content,
                user_id=message.author.id,
                conversation_history=None,  # History managed by session
                system_prompt=SYSTEM_PROMPT,
                sink=None,  # No sink for DMs
                force_respond=True,  # Always respond to DMs
                display_name=message.author.display_name,
                text_channel=message.channel,
                image_analysis_text=None,  # TODO: Add image analysis for DMs if needed
                chat_session=dm_session,  # Pass the DM session for statefulness
                mc_bridge=mc_bridge  # <-- Add this
            )
        except Exception as dm_err:
            logger.error(f"Error processing DM from {message.author.display_name}: {dm_err}", exc_info=True)
            await message.channel.send("sorry, i had trouble processing that dm")

        return  # Exit early for DMs, don't process through regular guild message logic

    # --- Check for Image or Video Mention ---
    image_processed = False
    if message.attachments and "luna" in message.content.lower():
        media_url = None
        is_video = False
        for attachment in message.attachments:
            # Basic check for image or video content types or extensions
            if attachment.content_type and attachment.content_type.startswith("image/"):
                media_url = attachment.url
                break # Process first image found
            elif attachment.filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                media_url = attachment.url
                break # Process first image found
            elif attachment.content_type and attachment.content_type.startswith("video/"):
                media_url = attachment.url
                is_video = True
                break # Process first video found
            elif attachment.filename.lower().endswith(('.mp4', '.mov', '.avi', '.webm', '.mkv')):
                media_url = attachment.url
                is_video = True
                break # Process first video found

        if media_url:
            logger.info(f"Processing media mention in channel {message.channel.id} from {message.author.display_name}. Media URL: {media_url}")
            try:
                if is_video:
                    # Download the video and extract 10 key frames (including first and last)
                    try:
                        import cv2
                        import numpy as np
                        import requests
                        from urllib.parse import urlparse
                        parsed_url = urlparse(media_url)
                        file_ext = os.path.splitext(parsed_url.path)[1]
                        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_vid:
                            response = requests.get(media_url, stream=True)
                            shutil.copyfileobj(response.raw, tmp_vid)
                            tmp_vid_path = tmp_vid.name
                        cap = cv2.VideoCapture(tmp_vid_path)
                        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        if total_frames > 0:
                            # Read all frames as grayscale for difference calculation
                            frame_grays = []
                            for idx in range(total_frames):
                                cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
                                ret, frame = cap.read()
                                if not ret:
                                    continue
                                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                                frame_grays.append(gray)
                            # Compute frame differences
                            diffs = [0]
                            for i in range(1, len(frame_grays)):
                                diff = np.sum(np.abs(frame_grays[i].astype(np.int16) - frame_grays[i-1].astype(np.int16)))
                                diffs.append(diff)
                            # Select 8 frames with highest difference (excluding first and last)
                            key_indices = set()
                            if len(frame_grays) > 2:
                                diff_indices = np.argsort(diffs[1:-1])[-8:] + 1  # skip first, last
                                key_indices.update(diff_indices.tolist())
                            # Always include first and last
                            key_indices.add(0)
                            key_indices.add(len(frame_grays)-1)
                            # Sort indices in order
                            key_indices = sorted(key_indices)
                            frame_bytes_list = []
                            for idx in key_indices:
                                cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
                                ret, frame = cap.read()
                                if ret:
                                    _, buf = cv2.imencode('.png', frame)
                                    frame_bytes_list.append(buf.tobytes())
                            if frame_bytes_list:
                                # Video analysis simplified for now
                                await message.channel.send("I can see you shared a video! Video analysis is being updated for better performance.")
                                session_key = f"text_{message.channel.id}"
                                session = bot.chat_sessions.get(session_key) if hasattr(bot, 'chat_sessions') else None
                                if session and "messages" in session:
                                    session["messages"].append({"role": "assistant", "content": video_summary})
                                return  # Prevent further processing (no main LLM call)
                            else:
                                await message.channel.send("Sorry, I couldn't extract frames from that video.")
                                return
                        else:
                            await message.channel.send("Sorry, I couldn't read that video file.")
                        cap.release()
                        os.remove(tmp_vid_path)
                    except ImportError:
                        await message.channel.send("Sorry, video analysis requires OpenCV (cv2) to be installed.")
                    except Exception as video_err:
                        logger.error(f"Error processing video attachment: {video_err}", exc_info=True)
                        await message.channel.send("Sorry, I had trouble analyzing that video.")
                else:
                    # Call vision model directly for image analysis
                    image_analysis = await llm_response.analyze_image_url_optimized(media_url)
                    if image_analysis:
                        await message.channel.send(image_analysis)
                        # Add to chat session for context
                        session_key = f"text_{message.channel.id}"
                        session = bot.chat_sessions.get(session_key) if hasattr(bot, 'chat_sessions') else None
                        if session and "messages" in session:
                            session["messages"].append({"role": "assistant", "content": image_analysis})
                    else:
                        await message.channel.send("Sorry, I couldn't analyze that image. My vision capabilities might be offline.")
                image_processed = True # Mark as processed to skip regular text handling
            except Exception as media_err:
                logger.error(f"Error processing media mention: {media_err}", exc_info=True)
                await message.channel.send("Sorry, I had trouble looking at that file.")
                image_processed = True

    # --- Process Regular Text Message (if not an image mention) ---
    if not image_processed:
        logger.info(f"Processing regular text message in channel {message.channel.id}: {message.content[:50]}...")

        # --- Check for Image or Video Attachments & Analyze Immediately ---
        attached_media_url = None
        is_video = False
        media_analysis_result = None # Store analysis result here
        if message.attachments:
            for attachment in message.attachments:
                is_image = False
                is_video = False
                if attachment.content_type and attachment.content_type.startswith("image/"):
                    is_image = True
                elif attachment.filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                    is_image = True
                elif attachment.content_type and attachment.content_type.startswith("video/"):
                    is_video = True
                elif attachment.filename.lower().endswith(('.mp4', '.mov', '.avi', '.webm', '.mkv')):
                    is_video = True

                if is_image or is_video:
                    attached_media_url = attachment.url
                    logger.info(f"Found media attachment URL: {attached_media_url}. Analyzing immediately...")
                    try:
                        if is_video:
                            try:
                                import cv2
                                import numpy as np
                                import requests
                                from urllib.parse import urlparse
                                parsed_url = urlparse(attached_media_url)
                                file_ext = os.path.splitext(parsed_url.path)[1]
                                with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_vid:
                                    response = requests.get(attached_media_url, stream=True)
                                    shutil.copyfileobj(response.raw, tmp_vid)
                                    tmp_vid_path = tmp_vid.name
                                cap = cv2.VideoCapture(tmp_vid_path)
                                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                                if total_frames > 0:
                                    # Read all frames as grayscale for difference calculation
                                    frame_grays = []
                                    for idx in range(total_frames):
                                        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
                                        ret, frame = cap.read()
                                        if not ret:
                                            continue
                                        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                                        frame_grays.append(gray)
                                    # Compute frame differences
                                    diffs = [0]
                                    for i in range(1, len(frame_grays)):
                                        diff = np.sum(np.abs(frame_grays[i].astype(np.int16) - frame_grays[i-1].astype(np.int16)))
                                        diffs.append(diff)
                                    # Select 8 frames with highest difference (excluding first and last)
                                    key_indices = set()
                                    if len(frame_grays) > 2:
                                        diff_indices = np.argsort(diffs[1:-1])[-8:] + 1  # skip first, last
                                        key_indices.update(diff_indices.tolist())
                                    # Always include first and last
                                    key_indices.add(0)
                                    key_indices.add(len(frame_grays)-1)
                                    # Sort indices in order
                                    key_indices = sorted(key_indices)
                                    frame_bytes_list = []
                                    for idx in key_indices:
                                        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
                                        ret, frame = cap.read()
                                        if ret:
                                            _, buf = cv2.imencode('.png', frame)
                                            frame_bytes_list.append(buf.tobytes())
                                    if frame_bytes_list:
                                        # Video analysis simplified for now
                                        await message.channel.send("I can see you shared a video! Video analysis is being updated for better performance.")
                                        session_key = f"text_{message.channel.id}"
                                        session = bot.chat_sessions.get(session_key) if hasattr(bot, 'chat_sessions') else None
                                        if session and "messages" in session:
                                            session["messages"].append({"role": "assistant", "content": video_summary})
                                        return  # Prevent further processing (no main LLM call)
                                    else:
                                        await message.channel.send("Sorry, I couldn't extract frames from that video.")
                                    cap.release()
                                    os.remove(tmp_vid_path)
                            except ImportError:
                                await message.channel.send("Sorry, video analysis requires OpenCV (cv2) to be installed.")
                            except Exception as video_err:
                                logger.error(f"Error processing video attachment: {video_err}", exc_info=True)
                                await message.channel.send("Sorry, I had trouble analyzing that video.")
                        else:
                            # Call vision model directly for image analysis
                            media_analysis_result = await llm_response.analyze_image_url_optimized(attached_media_url)
                            if media_analysis_result:
                                await message.channel.send(media_analysis_result)
                                # Add to chat session for context
                                session_key = f"text_{message.channel.id}"
                                session = bot.chat_sessions.get(session_key) if hasattr(bot, 'chat_sessions') else None
                                if session and "messages" in session:
                                    session["messages"].append({"role": "assistant", "content": media_analysis_result})
                            else:
                                await message.channel.send("Sorry, I couldn't analyze that image. My vision capabilities might be offline.")
                    except Exception as analysis_err:
                        logger.error(f"Error analyzing attached media {attached_media_url}: {analysis_err}", exc_info=True)
                        await message.channel.send("Sorry, I had trouble looking at that file.")
                    break # Process first media only

        # --- Log the text message AND image analysis (if any) to the database ---
        try:
            # Log original text message first
            llm_response.log_message_to_db( # Use imported function
                user_id=message.author.id,
                role="user",
                content=message.content, # Log original text content
                timestamp=message.created_at.timestamp(),
                channel_type="text",
                channel_id=message.channel.id
            )
            # Log image analysis separately if it exists
            if media_analysis_result:
                 llm_response.log_message_to_db( # Use imported function
                      user_id=message.author.id, # Associate with the same user
                      role="image_analysis", # Use a distinct role
                      content=f"[Analysis of {attached_media_url}]: {media_analysis_result}", # Log analysis text
                      timestamp=message.created_at.timestamp() + 0.001, # Slightly later timestamp
                      channel_type="text",
                      channel_id=message.channel.id
                 )
        except Exception as log_err:
            logger.error(f"Error during text/image analysis message logging: {log_err}", exc_info=True)
            # Continue processing even if logging fails

        # --- Call LLM Brain for Decision Making ---
        brain_action_taken = False
        brain_decision = None # Initialize brain_decision to handle potential errors before assignment
        try: # This try block covers brain query and action execution
            # --- Preprocess message content for brain: Resolve Aliases ---
            processed_message_content = message.content
            # Simple regex to find potential target names after action verbs
            # This is basic and might need refinement for complex sentences
            potential_target_match = re.search(r"(?:dm|tell|ask|kick|disconnect|call)\s+@?([\w\s]+)", message.content, re.IGNORECASE)
            if potential_target_match:
                potential_alias = potential_target_match.group(1).strip().lower()
                logger.debug(f"Potential target alias found in message: '{potential_alias}'")
                resolved_main_name = get_main_name_from_alias(potential_alias)
                # Check if resolution happened AND it's different from the original (case-insensitive check might be needed if main names can be aliases)
                # Let's assume resolved_main_name returns capitalized main name or capitalized original if no match
                if resolved_main_name and resolved_main_name.lower() != potential_alias:
                    logger.info(f"Resolved alias '{potential_alias}' to main name '{resolved_main_name}' for brain input.")
                    # Replace the *first* occurrence of the alias in the original message content
                    # This is a simple replacement, might replace unintended parts if alias is common word
                    # Consider more robust replacement if needed (e.g., only after the verb)
                    processed_message_content = message.content.replace(potential_target_match.group(1), resolved_main_name, 1)
                    logger.debug(f"Processed message content for brain: '{processed_message_content}'")
                else:
                    logger.debug(f"No specific main name found for potential alias '{potential_alias}', using original.")

            # Construct the user part of the prompt for the brain using processed content
            # TODO: Improve context (history, channel topic, etc.) later in Phase 3
            channel_description = f"'#{message.channel.name}'" if hasattr(message.channel, 'name') else "DM"
            user_prompt_content = f"User '{message.author.display_name}' ({message.author.id}) in channel {channel_description} ({message.channel.id}) said: '{processed_message_content}'" # Use processed content
            if media_analysis_result:
                user_prompt_content += f"\n\nImage Analysis: {media_analysis_result}"

            logger.info(f"Querying LLM Brain for decision on message ID {message.id}...")
            # Now get_brain_decision is async, so we await it directly
            brain_decision = await get_brain_decision(user_prompt_content)
            logger.info(f"LLM Brain Decision for message ID {message.id}: {brain_decision}")

            # --- Parse Brain Decision and Execute Action ---
            decision_lower = brain_decision.lower()

            # Example: Disconnect Action
            disconnect_match = re.search(r"(disconnect|kick)\s+(.*)", decision_lower) # Capture everything after keyword
            if disconnect_match:
                full_identifier_string = disconnect_match.group(2).strip() # e.g., "user gavin (921...)" or "<@921...>" or "gavin"
                logger.info(f"Brain decided to disconnect based on: '{full_identifier_string}'")

                target_member = None
                user_id_to_fetch = None
                name_to_search = None
                log_identifier = full_identifier_string # Default identifier for logs/messages

                # 1. Try extracting mention ID
                mention_id_match = re.search(r"<@!?(\d+)>", full_identifier_string)
                if mention_id_match:
                    try:
                        user_id_to_fetch = int(mention_id_match.group(1))
                        log_identifier = f"Mention ID {user_id_to_fetch}"
                    except ValueError:
                        logger.error(f"Invalid ID format in mention: {mention_id_match.group(1)}")

                # 2. If no mention ID, try extracting plain ID (17+ digits, possibly in brackets/parentheses)
                if not user_id_to_fetch:
                    plain_id_match = re.search(r"[\[\(]?(\d{17,})[\]\)]?", full_identifier_string)
                    if plain_id_match:
                        try:
                            user_id_to_fetch = int(plain_id_match.group(1))
                            log_identifier = f"Plain ID {user_id_to_fetch}"
                        except ValueError:
                            logger.error(f"Invalid ID format in plain ID: {plain_id_match.group(1)}")

                # --- Find Member ---
                # a. Try fetching by ID if found
                if user_id_to_fetch:
                    try:
                        logger.debug(f"Attempting to fetch member by ID: {user_id_to_fetch}")
                        target_member = await message.guild.fetch_member(user_id_to_fetch)
                    except (discord.NotFound, discord.HTTPException) as e:
                        logger.warning(f"Failed to fetch member by ID {user_id_to_fetch}: {e}")
                        target_member = None # Ensure it's None if fetch fails

                # b. Fallback: Try fetching by name (using the cleaned full string if no ID was used)
                if not target_member:
                    # Clean the original string for use as a name
                    name_to_search = re.sub(r'[<@!>#()\[\]]', '', full_identifier_string).strip() # Clean more chars
                    # Remove common prefixes like "user " if present
                    if name_to_search.lower().startswith("user "):
                        name_to_search = name_to_search[5:].strip()

                    if name_to_search: # Proceed only if name is not empty after cleaning
                        log_identifier = f"Name '{name_to_search}'" # Update log identifier if using name
                        try:
                            logger.debug(f"Attempting to get member by cleaned name: {name_to_search}")
                            target_member = message.guild.get_member_named(name_to_search)
                            # TODO: Could add smarter fuzzy matching or iteration over members if needed
                        except Exception as e: # Catch potential errors during name lookup
                            logger.error(f"Error during get_member_named for '{name_to_search}': {e}")
                            target_member = None
                    else:
                        # If cleaning resulted in empty string, don't attempt name search
                        logger.warning(f"Cleaned identifier '{full_identifier_string}' resulted in empty name string.")
                        log_identifier = full_identifier_string # Revert log identifier

                # --- Execute Disconnect ---
                if target_member:
                    if target_member.voice and target_member.voice.channel:
                         try:
                             await target_member.move_to(None, reason=f"Action requested by LLM Brain based on {message.author.display_name}'s message.")
                             await message.channel.send(f"✅ Disconnected {target_member.mention} as requested by the brain.")
                             logger.info(f"Successfully disconnected {target_member.display_name} ({target_member.id})")
                             brain_action_taken = True
                         except discord.Forbidden:
                             logger.warning(f"Missing permissions to disconnect {target_member.display_name}")
                             await message.channel.send(f"⚠️ I don't have permission to disconnect {target_member.mention}.")
                         except discord.HTTPException as e:
                             logger.error(f"Failed to disconnect {target_member.display_name}: {e}")
                             await message.channel.send(f"❌ Failed to disconnect {target_member.mention}.")
                    else:
                         logger.info(f"User {target_member.display_name} ({target_member.id}) not in a voice channel to disconnect.") # Use target_member details
                         await message.channel.send(f"ℹ️ {target_member.mention} isn't in a voice channel.")
                         # Decide if this counts as 'action taken' - maybe not? Let's say no for now.
                else:
                    # Use the identifier we tried to search with (ID or cleaned name)
                    logger.warning(f"Could not find user matching '{log_identifier}' to disconnect.")
                    await message.channel.send(f"❓ Couldn't find user matching '{log_identifier}' to disconnect.")

                brain_action_taken = True # Mark action attempted (even if finding/disconnect failed)

            # Example: DM Action
            # Regex to find identifier and message content after "dm"
            dm_match = re.search(r"dm\s+(.*)\s+(?:saying|tell(?:ing)?\s+(?:him|her|them)|with)\s*['\"]?(.*)['\"]?", decision_lower, re.IGNORECASE | re.DOTALL)
            if dm_match and not brain_action_taken: # Only process if disconnect didn't happen
                full_identifier_string = dm_match.group(1).strip()
                message_content = dm_match.group(2).strip()
                logger.info(f"Brain decided to DM based on '{full_identifier_string}' with message: '{message_content}'")

                target_user = None
                user_id_to_fetch = None
                name_to_search = None # Not directly used for fetch_user, but for logging/context
                log_identifier = full_identifier_string

                # 1. Try extracting mention ID
                mention_id_match = re.search(r"<@!?(\d+)>", full_identifier_string)
                if mention_id_match:
                    try:
                        user_id_to_fetch = int(mention_id_match.group(1))
                        log_identifier = f"Mention ID {user_id_to_fetch}"
                    except ValueError:
                        logger.error(f"Invalid ID format in mention for DM: {mention_id_match.group(1)}")

                # 2. If no mention ID, try extracting plain ID
                if not user_id_to_fetch:
                    plain_id_match = re.search(r"[\[\(]?(\d{17,})[\]\)]?", full_identifier_string)
                    if plain_id_match:
                        try:
                            user_id_to_fetch = int(plain_id_match.group(1))
                            log_identifier = f"Plain ID {user_id_to_fetch}"
                        except ValueError:
                            logger.error(f"Invalid ID format in plain ID for DM: {plain_id_match.group(1)}")

                # --- Find User ID (Prioritize ID lookup) ---
                # a. Try fetching by ID (global lookup)
                # 3. If no ID extracted yet, try alias lookup
                if not user_id_to_fetch:
                    # Clean the identifier string for alias lookup
                    alias_to_search = re.sub(r'[<@!>#()\[\]]', '', full_identifier_string).strip().lower()
                    if alias_to_search.startswith("user "):
                         alias_to_search = alias_to_search[5:].strip()

                    if alias_to_search: # Only search if alias string is not empty
                        logger.debug(f"Attempting alias lookup for DM target: '{alias_to_search}'")
                        found_id_from_alias = get_id_from_alias(alias_to_search)
                        if found_id_from_alias:
                            user_id_to_fetch = found_id_from_alias
                            log_identifier = f"Alias '{alias_to_search}' (ID: {user_id_to_fetch})"
                            logger.info(f"Found user ID {user_id_to_fetch} via alias '{alias_to_search}'.")
                        else:
                             logger.debug(f"Alias '{alias_to_search}' not found in USER_PROFILES.")
                    else:
                        logger.warning(f"Cleaned identifier '{full_identifier_string}' resulted in empty string for alias lookup.")
                        log_identifier = full_identifier_string # Revert log identifier if cleaning failed

                # --- Fetch User by ID (if found via mention, plain ID, or alias) ---
                if user_id_to_fetch:
                    try:
                        logger.debug(f"Attempting to fetch user by final ID for DM: {user_id_to_fetch}")
                        target_user = await bot.fetch_user(user_id_to_fetch) # Use bot.fetch_user for global lookup
                    except (discord.NotFound, discord.HTTPException) as e:
                        logger.warning(f"Failed to fetch user by final ID {user_id_to_fetch} for DM: {e}")
                        target_user = None # Ensure None on failure

                # --- Fallback: Try finding member by name/nickname in the current guild ---
                # This runs ONLY if no user was found via ID (mention, plain, or alias)
                if not target_user:
                    logger.debug(f"No user found via ID methods for '{log_identifier}'. Falling back to guild name/nickname search.")
                    # Use the same cleaned name as used for alias lookup (or re-clean if needed)
                    name_to_search_fallback = re.sub(r'[<@!>#()\[\]]', '', full_identifier_string).strip()
                    if name_to_search_fallback.lower().startswith("user "):
                        name_to_search_fallback = name_to_search_fallback[5:].strip()

                    if name_to_search_fallback: # Proceed only if name is not empty
                        log_identifier_fallback = f"Name/Nick '{name_to_search_fallback}' in Guild {message.guild.id}" # Update log identifier for fallback
                        try:
                            logger.debug(f"Attempting guild search for member by name/nick: '{name_to_search_fallback}'")
                            # Iterate through cached members (requires Members Intent)
                            found_in_guild = False
                            for member in message.guild.members:
                                if member.name.lower() == name_to_search_fallback.lower() or \
                                   (member.nick and member.nick.lower() == name_to_search_fallback.lower()):
                                    target_user = member # Found member object
                                    logger.info(f"Found potential DM target by name/nick in guild cache (fallback): {target_user.name} ({target_user.id})")
                                    log_identifier = log_identifier_fallback # Update final identifier used
                                    found_in_guild = True
                                    break
                            if not found_in_guild:
                                logger.warning(f"Fallback search: Member matching name/nick '{name_to_search_fallback}' not found in guild cache.")
                                target_user = None # Ensure target_user is None if not found by fallback
                        except Exception as e:
                            logger.error(f"Error during member iteration for DM fallback '{name_to_search_fallback}': {e}")
                            target_user = None
                    else:
                        logger.warning(f"Cleaned identifier '{full_identifier_string}' resulted in empty string for DM fallback name search.")
                        # log_identifier remains as it was before fallback attempt


                # --- Send DM ---
                if target_user: # Can be User or Member object now
                    try:
                        await target_user.send(message_content)
                        logger.info(f"Successfully sent DM to {target_user.name} ({target_user.id})")
                        await message.channel.send(f"✅ Sent DM to {target_user.mention}.")
                        brain_action_taken = True
                    except discord.Forbidden:
                        logger.warning(f"Cannot send DM to {target_user.name} ({target_user.id}) - Permissions/Privacy.")
                        await message.channel.send(f"⚠️ Couldn't send DM to {target_user.mention} (maybe they blocked me or have DMs disabled?).")
                        brain_action_taken = True # Still counts as action attempted
                    except discord.HTTPException as e:
                        logger.error(f"Failed to send DM to {target_user.name} ({target_user.id}): {e}")
                        await message.channel.send(f"❌ Failed to send DM to {target_user.mention} due to an API error.")
                        brain_action_taken = True # Still counts as action attempted
                else:
                    # If both ID and name lookup failed
                    logger.warning(f"Could not find user matching '{log_identifier}' to DM after checking ID and guild name.")
                    await message.channel.send(f"❓ Couldn't find user matching '{log_identifier}' to DM.")
                    brain_action_taken = True # Mark action attempted

            # Legacy call action parsing removed - now handled in llm_brain.py

            # Add more action parsing here if needed later

        # This except block handles errors during brain processing/action execution
        except Exception as brain_err:
            logger.error(f"Error during LLM Brain processing or action execution for message ID {message.id}: {brain_err}", exc_info=True)
            # Optional: Notify user about brain error
            # await message.channel.send("🧠 My decision circuits had an error.")
            # Decide if we should still attempt a default response. Let's allow it for now.
            # brain_action_taken = False # Explicitly allow fallback

        # --- If Brain Didn't Take Action (or failed and fallback is allowed), Proceed with Regular Response ---
        if not brain_action_taken:
            logger.info(f"LLM Brain took no specific action (or failed), proceeding with conversational response for message ID {message.id}.")

            # --- Get or Create Chat Session for Text Channel ---
            channel_id = message.channel.id
            session_key = f"text_{channel_id}"

            # Try to get session from bot.chat_sessions first (global access)
            session = bot.chat_sessions.get(session_key)

            # If not found in bot.chat_sessions, check text_chat_sessions (legacy)
            if session is None:
                session = text_chat_sessions.get(channel_id)
                # If found in legacy storage, migrate it to bot.chat_sessions
                if session is not None:
                    logger.info(f"Migrating session from text_chat_sessions to bot.chat_sessions for channel {channel_id}")
                    bot.chat_sessions[session_key] = session

            model_client = llm_response.get_llama_cpp_client() # Use getter function

            if session is None and model_client:
                logger.info(f"Creating new llama.cpp Chat Session for text channel {channel_id}.")
                try:
                    # Create a session ID for this channel
                    session_id = f"channel_{channel_id}_{int(time.time())}"

                    # Store the session ID and initialize message history (no system prompt needed)
                    session = {
                        "id": session_id,
                        "messages": []  # No system prompt needed - using llama.cpp with built-in system prompt
                    }

                    # Log session creation
                    logger.debug(f"Created new session with ID: {session_id}")

                    # Store in both dictionaries for compatibility
                    text_chat_sessions[channel_id] = session
                    bot.chat_sessions[session_key] = session

                    logger.info(f"Successfully created session for channel {channel_id}. Session has {len(session['messages'])} messages.")
                except Exception as e:
                    logger.error(f"Failed to create chat session for channel {channel_id}: {e}", exc_info=True)
                    session = None # Ensure session is None if creation failed
            elif not model_client:
                 logger.error(f"Cannot create/use session for channel {channel_id}: llama.cpp client not initialized.")
                 session = None
            else:
                 # Session exists, log its current state
                 logger.info(f"Using existing chat session for channel {channel_id}. Session has {len(session['messages'])} messages.")

                 # Log the first few messages in the session for debugging
                 if len(session["messages"]) > 0:
                     logger.info(f"First message in session: role={session['messages'][0].get('role')}, content={session['messages'][0].get('content')[:50]}...")
                 if len(session["messages"]) > 1:
                     logger.info(f"Second message in session: role={session['messages'][1].get('role')}, content={session['messages'][1].get('content')[:50]}...")

            # --- DECISION SYSTEM FOR GUILD TEXT CHANNELS (NOT DMs) ---
            should_luna_respond = True  # Default to True (for DMs or fallback)
            
            # Only apply decision system to guild text channels, not DMs
            if hasattr(message.channel, 'guild') and message.channel.guild is not None:
                logger.info(f"🤔 Applying decision system to guild text message from {message.author.display_name} in {message.channel.name}")
                
                try:
                    # Import the decision function
                    from llm_response.decision import should_respond
                    
                    # Build conversation history from the session
                    conversation_history = []
                    if session and "messages" in session:
                        conversation_history = session["messages"][-20:]  # Use last 20 messages for context
                    
                    # Build speaker turn history from recent messages
                    # Since we don't have the same speaker tracking as voice, we'll simulate it from recent messages
                    speaker_turn_history = []
                    if conversation_history:
                        current_speaker = None
                        for msg in conversation_history[-10:]:  # Look at last 10 messages
                            role = msg.get('role', 'unknown')
                            content = msg.get('content', '')
                            
                            if role == 'user':
                                display_name = message.author.display_name  # Current message author
                                user_id = str(message.author.id)
                            elif role == 'assistant':
                                display_name = "Luna"
                                user_id = str(bot.user.id)
                            else:
                                continue  # Skip other roles
                            
                            # Add to speaker turn history if different from current speaker
                            if current_speaker != display_name:
                                speaker_turn_history.append({
                                    'display_name': display_name,
                                    'user_id': user_id,
                                    'content_preview': content[:50]
                                })
                                current_speaker = display_name
                    
                    # Add current message to speaker turn history
                    speaker_turn_history.append({
                        'display_name': message.author.display_name,
                        'user_id': str(message.author.id),
                        'content_preview': message.content[:50]
                    })
                    
                    # Check if Luna should respond using the decision system
                    should_luna_respond = await should_respond(
                        text=message.content,
                        current_speaker_id=str(message.author.id),
                        conversation_history=conversation_history,
                        speaker_turn_history=speaker_turn_history,
                        is_currently_speaking=False,  # Not applicable for text
                        channel_id=channel_id,
                        confidence_threshold=None  # Use default from config
                    )
                    
                    if should_luna_respond:
                        logger.info(f"✅ Decision: Luna will respond to text message from {message.author.display_name}")
                    else:
                        logger.info(f"❌ Decision: Luna will NOT respond to text message from {message.author.display_name}")
                        
                except Exception as decision_err:
                    logger.error(f"Error in decision system for text message: {decision_err}", exc_info=True)
                    should_luna_respond = True  # Fallback to responding on error
                    logger.info("Falling back to responding due to decision system error")
            else:
                logger.info(f"📩 Skipping decision system for DM from {message.author.display_name} (always respond to DMs)")

            # --- Only proceed with response if decision system says to respond ---
            if should_luna_respond:
                # --- Call llm_response.process_user_message ---
                try:
                    # Prepare a summary of recent Minecraft events
                    mc_context_lines = []
                    for event_type, data in list(mc_event_history)[-10:]:
                        if event_type == "public_chat":
                            mc_context_lines.append(f"[MC Chat] {data.get('message')}")
                        elif event_type == "action_start":
                            mc_context_lines.append(f"[MC Action Start] {data.get('action')}")
                        elif event_type == "action_end":
                            mc_context_lines.append(f"[MC Action End] {data.get('action')} ({data.get('status')})")
                        elif event_type == "thought":
                            mc_context_lines.append(f"[MC Thought] {data.get('thought')}")
                    mc_context = '\n'.join(mc_context_lines)
                    if mc_context:
                        # Make it explicit these are Luna's own actions
                        prompt_with_mc = (
                            "You are Luna, and the following is a summary of your recent actions, thoughts, and chat in Minecraft. "
                            "Use this to inform your response. These are YOUR actions, not the user's.\n"
                            "[Your Recent Minecraft Activity]\n"
                            f"{mc_context}\n\n"
                            f"{message.content}"
                        )
                    else:
                        prompt_with_mc = message.content

                    # Then call process_user_message with prompt_with_mc instead of message.content
                    await llm_response.process_user_message(
                        bot=bot,
                        text=prompt_with_mc,
                        user_id=message.author.id,
                        conversation_history=None, # History is managed by session or stateless call
                        system_prompt=SYSTEM_PROMPT, # Pass for stateless fallback if session is None
                        sink=None, # No sink for text messages
                        force_respond=True, # Force respond since we already made the decision here
                        display_name=message.author.display_name,
                        text_channel=message.channel,
                        image_analysis_text=media_analysis_result, # Pass analysis if available
                        chat_session=session, # Pass the retrieved/created session
                        brain_decision_context=brain_decision, # Pass the brain's decision context
                        mc_bridge=mc_bridge  # <-- Add this
                    )
                except Exception as llm_err:
                    logger.error(f"Error calling process_user_message for text message {message.id}: {llm_err}", exc_info=True)
                    await message.channel.send("Sorry, I had trouble processing that.")
            else:
                logger.info(f"🚫 Skipping response to text message from {message.author.display_name} based on decision system")

        # --- Minecraft command forwarding (only if Luna responded) ---
        if should_luna_respond and is_minecraft_mode_enabled() and mc_bridge is not None:
            # If message mentions 'luna' (case-insensitive) and is not a command, forward to Minecraft
            if (
                "luna" in message.content.lower()
                and not message.author.bot
                and not message.content.strip().startswith(bot.command_prefix)
                and mc_bridge is not None
            ):
                try:
                    # Remove 'luna' (case-insensitive) and any punctuation/whitespace after it
                    cleaned = re.sub(r"(?i)luna[,:;\\-\\s]*", "", message.content, count=1).strip()
                    mc_bridge.send_command(cleaned)
                    logger.info(f"Forwarded to Minecraft: {cleaned}")
                except Exception as e:
                    logger.error(f"Failed to forward to Minecraft: {e}")

@bot.event
async def on_close():
    """Clean up resources when bot is shutting down"""
    logger.info("Bot shutting down, cleaning up resources...")
    
    # Clean up auto-screenshot system
    try:
        await auto_screenshot_manager.stop()
        logger.info("✅ Auto-screenshot system stopped")
    except Exception as e:
        logger.warning(f"Error stopping auto-screenshot system: {e}")
    
    # Memory cleanup no longer needed
    
    # Clean up background tasks
    if hasattr(bot, '_background_tasks'):
        logger.info(f"Cleaning up {len(bot._background_tasks)} background tasks")
        for task in list(bot._background_tasks):
            if not task.done():
                task.cancel()
        if bot._background_tasks:
            await asyncio.gather(*bot._background_tasks, return_exceptions=True)
        bot._background_tasks.clear()
        logger.info("✅ Background tasks cleaned up")
    
    await close_tts_engine()
    logger.info("Bot shutdown cleanup completed")

# Memory system disabled - no longer using Graphiti/Neo4j
async def initialize_memory_systems():
    logger.info("Memory system disabled - no longer using Graphiti/Neo4j")
    return None

# Initialize the Minecraft bridge at startup ONLY if enabled
mc_bridge = None
if is_minecraft_mode_enabled():
    from luna_mc_bridge import LunaMCBridge
    mc_bridge = LunaMCBridge(agent_name="lunapotato")
    try:
        mc_bridge.connect()
        mc_bridge.start_background_listener()
        print("[Luna] Connected to MindServer for Minecraft integration.")
        logger.info(f"[DEBUG] mc_bridge initialized: {mc_bridge}")
    except Exception as e:
        print(f"[Luna] Failed to connect to MindServer: {e}")
        mc_bridge = None

# Add a Discord command to send Minecraft commands
@bot.command()
async def mc(ctx, *, command: str):
    if not is_minecraft_mode_enabled() or mc_bridge is None:
        await ctx.send("Minecraft bridge is not connected or Minecraft mode is disabled.")
        return
    try:
        mc_bridge.send_command(command)
        await ctx.send(f"Sent to Minecraft: {command}")
    except Exception as e:
        await ctx.send(f"Failed to send command: {e}")

@bot.command()
async def latency_test(ctx):
    """Test the comprehensive latency measurement system with all tracking points"""
    try:
        from llm_response.latency_tracker import (
            start_latency_tracking, mark_latency_timestamp, set_response_length,
            complete_latency_tracking, generate_latency_report, format_latency_report_for_discord
        )
        from llm_response.config import PROMPT_LOG_CHANNEL_ID
        import time
        
        logger.info(f"🧪 Running comprehensive latency test requested by {ctx.author}")
        
        # Start latency tracking for this test
        turn_id = start_latency_tracking(str(ctx.author.id), ctx.author.display_name)
        
        # Simulate complete voice interaction pipeline
        mark_latency_timestamp("audio_detected")
        await asyncio.sleep(0.01)
        
        # Transcription
        mark_latency_timestamp("transcription_end")
        await asyncio.sleep(0.005)
        
        # Processing pipeline
        mark_latency_timestamp("process_start")
        await asyncio.sleep(0.002)
        
        # Brain decision system
        mark_latency_timestamp("brain_start")
        mark_latency_timestamp("brain_api_start")
        await asyncio.sleep(0.15)  # Simulate brain API call
        mark_latency_timestamp("brain_api_end")
        mark_latency_timestamp("brain_decision_start")
        await asyncio.sleep(0.02)  # Simulate brain decision processing
        mark_latency_timestamp("brain_decision_end")
        mark_latency_timestamp("brain_end")
        await asyncio.sleep(0.005)
        
        # Decision-making system
        mark_latency_timestamp("decision_start")
        mark_latency_timestamp("decision_api_start")
        await asyncio.sleep(0.08)  # Simulate decision API call
        mark_latency_timestamp("decision_api_end")
        mark_latency_timestamp("decision_end")
        await asyncio.sleep(0.01)
        
        # Memory system
        mark_latency_timestamp("memory_search_start")
        await asyncio.sleep(0.03)  # Simulate memory search
        mark_latency_timestamp("memory_search_end")
        await asyncio.sleep(0.01)
        
        # LLM generation
        mark_latency_timestamp("llm_api_start")
        await asyncio.sleep(0.4)  # Simulate LLM processing
        mark_latency_timestamp("first_token")
        await asyncio.sleep(0.6)  # Simulate more LLM generation
        mark_latency_timestamp("llm_api_end")
        await asyncio.sleep(0.01)
        
        # VTube Studio integration
        mark_latency_timestamp("emotion_detection_start")
        await asyncio.sleep(0.008)  # Simulate emotion detection
        mark_latency_timestamp("emotion_detection_end")
        mark_latency_timestamp("emotion_context_start")
        await asyncio.sleep(0.005)  # Simulate context processing
        mark_latency_timestamp("emotion_context_end")
        mark_latency_timestamp("vts_emotion_start")
        await asyncio.sleep(0.012)  # Simulate VTS emotion processing
        mark_latency_timestamp("vts_emotion_end")
        mark_latency_timestamp("vts_hotkey_start")
        mark_latency_timestamp("vts_api_call_start")
        await asyncio.sleep(0.025)  # Simulate VTS API call
        mark_latency_timestamp("vts_api_call_end")
        mark_latency_timestamp("vts_hotkey_end")
        
        # TTS pipeline
        mark_latency_timestamp("tts_first_chunk")
        await asyncio.sleep(0.03)
        mark_latency_timestamp("tts_player_start")
        await asyncio.sleep(0.02)
        mark_latency_timestamp("audio_playback_start")
        
        # Set response metadata
        set_response_length(250)
        
        # Complete the turn and generate report
        metrics = complete_latency_tracking()
        
        if metrics:
            # Generate comprehensive report
            report = generate_latency_report(metrics)
            formatted_report = format_latency_report_for_discord(report)
            
            # Send to latency reports channel if available
            if PROMPT_LOG_CHANNEL_ID:
                try:
                    channel = bot.get_channel(int(PROMPT_LOG_CHANNEL_ID))
                    if channel:
                        embed = discord.Embed(
                            title="🧪 Comprehensive Latency Tracking Test",
                            description=formatted_report,
                            color=0x00ff00,
                            timestamp=datetime.utcnow()
                        )
                        await channel.send(embed=embed)
                        await ctx.send("✅ **Comprehensive latency test completed successfully!** Check the latency reports channel for the detailed test report including Brain, Decision-making, Memory, and VTube Studio metrics.")
                    else:
                        await ctx.send(f"✅ **Test completed!** Could not find channel {PROMPT_LOG_CHANNEL_ID}")
                except Exception as channel_err:
                    logger.warning(f"Could not send test report to channel: {channel_err}")
                    await ctx.send("✅ **Test completed!** Could not send to reports channel, but test was successful.")
            else:
                await ctx.send("✅ **Test completed!** No reports channel configured, but test was successful.")
                
            logger.info(f"🧪 Comprehensive latency test completed successfully!\n{formatted_report}")
        else:
            await ctx.send("❌ **Test failed** - no metrics returned")
            logger.error("🧪 Test failed - no metrics returned")
        
    except Exception as e:
        await ctx.send(f"❌ **Error running comprehensive latency test:** {e}")
        logger.error(f"Error in latency_test command: {e}", exc_info=True)

# Update process_mc_events to store events in history
def process_mc_events():
    if mc_bridge is None:
        return
    while True:
        event = mc_bridge.get_event()
        if event:
            event_type, data = event
            mc_event_history.append((event_type, data))
            print(f"[MC EVENT] {event_type}: {data}")
            # TODO: Integrate with Luna's context/logic as needed

# Start the event processor in a background thread ONLY if enabled
if is_minecraft_mode_enabled():
    import threading
    t = threading.Thread(target=process_mc_events, daemon=True)
    t.start()

if __name__ == "__main__":
    print(">>> Starting main execution block <<<") # Added print
    try: # Added try block
        # Warm up GPU with Vulkan backend
        print(">>> Calling warmup_gpu (Vulkan) <<<") # Added print
        warmup_gpu()
        print(">>> Finished warmup_gpu (Vulkan) <<<") # Added print
        
        # PRIORITY: Warm up luna-tune:latest model FIRST to claim maximum GPU memory
        print(">>> Calling warmup_luna_tune_model <<<") # Added print
        warmup_luna_tune_model()
        print(">>> Finished warmup_luna_tune_model <<<") # Added print
        
        # Then warm up the transcription model (will use remaining VRAM)
        print(">>> Calling warmup_transcription_model <<<") # Added print
        warmup_transcription_model()
        print(">>> Finished warmup_transcription_model <<<") # Added print
        
        # --- NEW: Warm up Kokoro TTS engine so the first spoken response is instantaneous
        print(">>> Calling prewarm_tts_engine <<<")
        try:
            if prewarm_tts_engine():
                print(">>> KokoroEngine prewarm succeeded <<<")
            else:
                print("!!! KokoroEngine prewarm failed – engine not ready !!!")
        except Exception as tts_warm_err:
            logger.warning(f"TTS prewarm encountered an error: {tts_warm_err}")
            print(f"!!! KokoroEngine prewarm error: {tts_warm_err} !!!")
        print(">>> Finished prewarm_tts_engine <<<")
        
        # Run the bot
        print(">>> Calling bot.run <<<") # Added print
        bot.run(DISCORD_BOT_TOKEN)
        print(">>> bot.run finished <<<") # Added print (might not be reached if bot runs forever)
    except Exception as main_err: # Added except block
        print(f"!!! ERROR in main execution block: {main_err} !!!") # Added print
        logger.exception("Error during main execution:") # Log the full traceback
    finally: # Added finally block
        print(">>> Exiting main execution block <<<") # Added print


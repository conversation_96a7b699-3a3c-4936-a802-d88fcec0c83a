#!/usr/bin/env python3
"""
Test Script for /no_think Improvement
====================================

This script tests the smart lights brain with the /no_think directive
to see if it reduces thinking text and improves JSON response quality.
"""

import asyncio
import logging
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_no_think_responses():
    """Test the smart lights brain responses with /no_think directive."""
    print("\n=== Testing /no_think Directive ===")
    
    try:
        from llm_response.smart_lights_brain import process_lights_command, get_lights_brain_decision
        from llm_response.smart_lights import get_available_bulbs
        
        available_bulbs = get_available_bulbs()
        print(f"Available bulbs: {available_bulbs}")
        
        # Test commands that previously generated thinking text
        test_commands = [
            "turn on closet light",
            "turn off bedroom light", 
            "make living room red",
            "set kitchen light blue at 50%",
            "dim the office light to purple"
        ]
        
        print("\nTesting brain responses with /no_think:")
        
        for i, cmd in enumerate(test_commands, 1):
            print(f"\n--- Test {i}: '{cmd}' ---")
            
            try:
                # Get raw brain decision to see the actual model output
                raw_response = await get_lights_brain_decision(cmd)
                print(f"Raw Response: {raw_response}")
                
                # Check for thinking patterns
                has_thinking = any(pattern in raw_response.lower() for pattern in [
                    "let me", "i need to", "first", "okay", "the user", 
                    "should", "check", "make sure", "double-check"
                ])
                
                thinking_status = "❌ Contains thinking" if has_thinking else "✅ No thinking detected"
                print(f"Thinking Analysis: {thinking_status}")
                
                # Check for JSON format
                has_json = "{" in raw_response and "}" in raw_response
                json_status = "✅ Contains JSON" if has_json else "❌ No JSON found"
                print(f"JSON Analysis: {json_status}")
                
                # Process the full command
                result = await process_lights_command(cmd)
                success_status = "✅ Success" if result.get('success') else "❌ Failed"
                print(f"Command Result: {success_status}")
                
                if result.get('success'):
                    function_name = result.get('function', 'unknown')
                    arguments = result.get('arguments', {})
                    print(f"  Function: {function_name}")
                    print(f"  Arguments: {arguments}")
                    
                    # Execute the actual light control (but turn it off immediately to avoid leaving lights on)
                    if function_name == 'turn_on':
                        await asyncio.sleep(0.5)  # Brief delay
                        # Turn it back off
                        from llm_response.smart_lights import turn_off
                        bulb_name = arguments.get('name', '')
                        if bulb_name:
                            turn_off(bulb_name)
                            print(f"  (Turned off {bulb_name} after test)")
                
            except Exception as e:
                print(f"❌ Error processing command '{cmd}': {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during /no_think testing: {e}")
        return False

async def test_response_quality_comparison():
    """Compare response quality metrics."""
    print("\n=== Response Quality Analysis ===")
    
    try:
        from llm_response.smart_lights_brain import get_lights_brain_decision
        
        # Test a few commands and analyze response quality
        test_commands = [
            "turn on closet light",
            "make bedroom red at 75%",
            "turn off kitchen light"
        ]
        
        total_responses = len(test_commands)
        clean_responses = 0
        json_responses = 0
        successful_parses = 0
        
        for cmd in test_commands:
            try:
                response = await get_lights_brain_decision(cmd)
                
                # Check if response is clean (minimal thinking text)
                thinking_words = ["let me", "i need", "first", "okay", "the user", "should", "check"]
                thinking_count = sum(1 for word in thinking_words if word in response.lower())
                
                if thinking_count <= 1:  # Allow minimal thinking
                    clean_responses += 1
                
                # Check if response contains JSON
                if "{" in response and "}" in response:
                    json_responses += 1
                
                # Check if we can parse it successfully
                from llm_response.smart_lights_brain import parse_lights_function_call
                func_name, args = parse_lights_function_call(response)
                if func_name and args:
                    successful_parses += 1
                
            except Exception as e:
                print(f"Error analyzing response for '{cmd}': {e}")
        
        print(f"\nQuality Metrics:")
        print(f"  Clean Responses: {clean_responses}/{total_responses} ({clean_responses/total_responses*100:.1f}%)")
        print(f"  JSON Responses: {json_responses}/{total_responses} ({json_responses/total_responses*100:.1f}%)")
        print(f"  Successful Parses: {successful_parses}/{total_responses} ({successful_parses/total_responses*100:.1f}%)")
        
        # Consider it successful if we get good results
        return successful_parses >= total_responses * 0.8  # 80% success rate
        
    except Exception as e:
        print(f"❌ Error during quality analysis: {e}")
        return False

async def main():
    """Run all /no_think tests."""
    print("🔍 Testing /no_think Directive Improvements")
    print("=" * 50)
    
    tests = [
        ("No Think Responses", test_no_think_responses),
        ("Response Quality", test_response_quality_comparison),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            print(f"💥 {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 /no_think TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 /no_think directive is working effectively!")
        print("💡 The model should now generate cleaner, more direct JSON responses.")
    else:
        print("⚠️ Some issues remain. The /no_think directive may need adjustment.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

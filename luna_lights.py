#!/usr/bin/env python3
import json
import re
import sys
from lifxlan import LifxLA<PERSON>
from llama_cpp import Llama

# ── 1) Discover your bulbs on the LAN with debugging ────────────────────────
try:
    lan = LifxLAN(3)
    bulbs = {b.get_label(): b for b in lan.get_lights()}
except Exception as e:
    print(f"Error discovering bulbs: {e}")
    sys.exit(1)

# Debug: print all discovered bulbs
print("Discovered bulbs:")
for label in bulbs:
    print(f" - {label}")
print("")

# ── 2) Define your bulb-control functions with debug prints ─────────────────────
def turn_on(name: str):
    print(f"[DEBUG] turn_on called with name='{name}'")
    bulb = bulbs.get(name)
    if not bulb:
        print(f"[ERROR] No bulb found with label '{name}'")
        return {"error": f"No bulb named '{name}'"}
    bulb.set_power("on")
    print(f"[INFO] Turned on '{name}'")
    return {"status": f"Turned on {name}"}

def turn_off(name: str):
    print(f"[DEBUG] turn_off called with name='{name}'")
    bulb = bulbs.get(name)
    if not bulb:
        print(f"[ERROR] No bulb found with label '{name}'")
        return {"error": f"No bulb named '{name}'"}
    bulb.set_power("off")
    print(f"[INFO] Turned off '{name}'")
    return {"status": f"Turned off {name}"}

def set_color(name: str, color: dict, brightness: int = 100):
    print(f"[DEBUG] set_color called with name='{name}', color={color}, brightness={brightness}")
    bulb = bulbs.get(name)
    if not bulb:
        print(f"[ERROR] No bulb found with label '{name}'")
        return {"error": f"No bulb named '{name}'"}
    # Simple RGB→HSBK mapping
    h = int((color["r"] / 255) * 65535)
    s = int((color["g"] / 255) * 65535)
    b = int((brightness / 100) * 65535)
    k = 3500
    bulb.set_color((h, s, b, k))
    print(f"[INFO] Set '{name}' to {color} @ {brightness}% brightness")
    return {"status": f"Set {name} to {color} at {brightness}% brightness"}

# ── 3) List of function schemas ──────────────────────────────────────────
FUNCTIONS = [
    {
        "name": "turn_on",
        "description": "Turn a LIFX bulb on",
        "parameters": {
            "type": "object",
            "properties": {"name": {"type": "string", "description": "Bulb label"}},
            "required": ["name"]
        }
    },
    {
        "name": "turn_off",
        "description": "Turn a LIFX bulb off",
        "parameters": {
            "type": "object",
            "properties": {"name": {"type": "string", "description": "Bulb label"}},
            "required": ["name"]
        }
    },
    {
        "name": "set_color",
        "description": "Set the bulb’s color & brightness",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "color": {
                    "type": "object",
                    "properties": {
                        "r": {"type": "integer", "minimum": 0, "maximum": 255},
                        "g": {"type": "integer", "minimum": 0, "maximum": 255},
                        "b": {"type": "integer", "minimum": 0, "maximum": 255}
                    },
                    "required": ["r", "g", "b"]
                },
                "brightness": {"type": "integer", "minimum": 0, "maximum": 100}
            },
            "required": ["name", "color"]
        }
    }
]

# ── 4) Load Qwen3 with GPU and function-calling format ──────────────────────────────────────────
model = Llama(
    model_path=(
        "C:/Users/<USER>/.lmstudio/models/lmstudio-community/"
        "Qwen3-4B-GGUF/Qwen3-4B-Q4_K_M.gguf"
    ),
    n_ctx=2048,
    n_threads=8,
    n_gpu_layers=-1,
    chat_format="chatml-function-calling",
    verbose=False
)

# ── 5) System prompt & chat history ────────────────────────────────────
system = {
    "role": "system",
    "content": (
        "/no_think\n"
        "You are Luna, a smart-home assistant. For light control commands, respond only with a JSON function_call matching the FUNCTIONS schema."
    )
}
messages = [system]

# ── 6) Helpers to parse & normalize function calls ─────────────────────

def parse_call(choice):
    # 1) prefer llama_cpp tool_calls if present
    if "tool_calls" in choice and choice["tool_calls"]:
        func = choice["tool_calls"][0]["function"]
        name = func.get("name", "")
        args = json.loads(func.get("arguments", "{}"))
        return name, args
    # 2) explicit function_call field (strip trailing colons)
    if "function_call" in choice and choice["function_call"].get("name"):
        name = choice["function_call"]["name"].rstrip(':')
        args = json.loads(choice["function_call"]["arguments"])
        return name, args
    # 3) strip think tags and extract raw JSON
    txt = re.sub(r"</?think.*?>", "", choice.get("content", ""), flags=re.DOTALL | re.IGNORECASE)
    m = re.search(r"(\{.*\})", txt, flags=re.DOTALL)
    if not m:
        return None, None
    fc = json.loads(m.group(1))
    if "function" in fc:
        return fc["function"], fc.get("args") or fc.get("arguments") or {}
    if "name" in fc:
        return fc["name"], fc.get("arguments") or fc.get("args") or {}
    return None, None


def normalize_args(args):  # unify name matching ignoring case(args):
    # unify name matching ignoring case
    if "name" in args:
        raw = args.get("name")
        for label in bulbs:
            if label.lower() == raw.lower():
                args["name"] = label
                break
    return args

# ── 7) Main chat function with enforced JSON response ───────────────────────────────────────

def user_ask(prompt):
    messages.append({"role": "user", "content": prompt})
    resp = model.create_chat_completion(
        messages=messages,
        functions=FUNCTIONS,
        function_call="auto",
        response_format={"type": "json_object"}
    )
    choice = resp["choices"][0]["message"]
    print(f"[DEBUG] LLM message payload: {choice}")
    fname, fargs = parse_call(choice)
    if fname:
        fargs = normalize_args(fargs)
        if fname in globals():
            result = globals()[fname](**fargs)
        else:
            result = {"error": f"Unknown function '{fname}'"}
        messages.append(choice)
        messages.append({"role": "function", "name": fname, "content": json.dumps(result)})
        output = {"function_call": {"name": fname, "arguments": fargs}}
        print(json.dumps(output))
    else:
        print(f"[DEBUG] No function call detected. Content: {choice.get('content', '')}")
        print(choice.get("content", ""))

if __name__ == "__main__":
    print("🔊 Start by confirming that 'Closet Light' appears above.")
    print("🔊 Try: Luna, turn on my Closet Light and make it red at 50% brightness.")
    while True:
        try:
            text = input("You: ")
        except (EOFError, KeyboardInterrupt):
            break
        if text.lower() in ("exit", "quit"):
            break
        user_ask(text)
# good
#!/usr/bin/env python3
"""
Test Script for Smart Lights Improvements
=========================================

This script tests the improved smart lights functionality,
focusing on proper bulb selection and color handling.
"""

import asyncio
import logging
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_individual_bulb_control():
    """Test controlling individual bulbs without affecting others."""
    print("\n=== Testing Individual Bulb Control ===")
    
    try:
        from llm_response.smart_lights import discover_bulbs, turn_on, turn_off, set_color, get_color_by_name
        
        # Discover bulbs
        bulbs = discover_bulbs(force_refresh=True)
        available_bulbs = list(bulbs.keys())
        print(f"Available bulbs: {available_bulbs}")
        
        if len(available_bulbs) < 2:
            print("⚠️ Need at least 2 bulbs for individual control testing")
            return False
        
        # Test individual control
        bulb1 = available_bulbs[0]
        bulb2 = available_bulbs[1]
        
        print(f"\nTesting individual control:")
        print(f"Bulb 1: {bulb1}")
        print(f"Bulb 2: {bulb2}")
        
        # Turn on first bulb only
        print(f"\n1. Turning on {bulb1} only...")
        result = turn_on(bulb1)
        print(f"Result: {result}")
        await asyncio.sleep(2)
        
        # Set first bulb to green
        print(f"\n2. Setting {bulb1} to green...")
        green_color = get_color_by_name("green")
        result = set_color(bulb1, green_color, 75)
        print(f"Result: {result}")
        await asyncio.sleep(2)
        
        # Turn on second bulb (should not change color)
        print(f"\n3. Turning on {bulb2} (should not affect {bulb1})...")
        result = turn_on(bulb2)
        print(f"Result: {result}")
        await asyncio.sleep(2)
        
        # Set second bulb to blue
        print(f"\n4. Setting {bulb2} to blue...")
        blue_color = get_color_by_name("blue")
        result = set_color(bulb2, blue_color, 50)
        print(f"Result: {result}")
        await asyncio.sleep(3)
        
        # Turn off both bulbs
        print(f"\n5. Turning off both bulbs...")
        result1 = turn_off(bulb1)
        result2 = turn_off(bulb2)
        print(f"Bulb 1 off: {result1}")
        print(f"Bulb 2 off: {result2}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during individual bulb control testing: {e}")
        return False

async def test_color_conversion():
    """Test RGB to HSBK color conversion."""
    print("\n=== Testing Color Conversion ===")
    
    try:
        from llm_response.smart_lights import rgb_to_hsbk, get_color_by_name, PREDEFINED_COLORS
        
        print("Testing predefined colors:")
        for color_name, rgb_values in PREDEFINED_COLORS.items():
            r, g, b = rgb_values['r'], rgb_values['g'], rgb_values['b']
            h, s, br, k = rgb_to_hsbk(r, g, b, 100)
            print(f"  {color_name}: RGB({r},{g},{b}) -> HSBK({h},{s},{br},{k})")
        
        print("\nTesting color name lookup:")
        test_colors = ["red", "green", "blue", "purple", "warm white", "invalid_color"]
        for color_name in test_colors:
            color = get_color_by_name(color_name)
            status = "✅" if color else "❌"
            print(f"  {status} '{color_name}' -> {color}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during color conversion testing: {e}")
        return False

async def test_improved_brain():
    """Test the improved smart lights brain."""
    print("\n=== Testing Improved Smart Lights Brain ===")
    
    try:
        from llm_response.smart_lights_brain import process_lights_command, extract_bulb_name_from_command
        from llm_response.smart_lights import get_available_bulbs
        
        available_bulbs = get_available_bulbs()
        print(f"Available bulbs: {available_bulbs}")
        
        # Test bulb name extraction
        print("\nTesting bulb name extraction:")
        test_commands = [
            "turn on closet light",
            "make the bedroom red",
            "turn off kitchen",
            "set living room to blue"
        ]
        
        for cmd in test_commands:
            extracted_name = extract_bulb_name_from_command(cmd)
            print(f"  '{cmd}' -> '{extracted_name}'")
        
        # Test brain processing with specific bulb commands
        print("\nTesting brain processing:")
        if available_bulbs:
            specific_commands = [
                f"turn on {available_bulbs[0]}",
                f"turn off {available_bulbs[0]}",
                f"make {available_bulbs[0]} green at 50%"
            ]
            
            for cmd in specific_commands:
                print(f"\nProcessing: '{cmd}'")
                result = await process_lights_command(cmd)
                print(f"Result: {result}")
                
                if result.get('success'):
                    print("✅ Command processed successfully")
                    # Small delay between commands
                    await asyncio.sleep(1)
                else:
                    print("⚠️ Command processing failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during brain testing: {e}")
        return False

async def main():
    """Run all improvement tests."""
    print("🔍 Testing Smart Lights Improvements")
    print("=" * 50)
    
    tests = [
        ("Color Conversion", test_color_conversion),
        ("Individual Bulb Control", test_individual_bulb_control),
        ("Improved Brain", test_improved_brain),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            print(f"💥 {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 IMPROVEMENT TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All improvement tests passed!")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

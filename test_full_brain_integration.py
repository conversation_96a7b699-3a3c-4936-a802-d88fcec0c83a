#!/usr/bin/env python3
"""
Full Brain Integration Test
==========================

Test the complete brain integration including lights control execution.
"""

import asyncio
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_full_integration():
    """Test the full brain integration with lights control."""
    print("Testing full brain integration...")
    
    try:
        import llm_brain
        
        # Test the brain decision and parsing
        test_prompt = 'User <PERSON> said: "Luna turn off my closet light" in channel #luna-commands'
        raw_decision = await llm_brain.get_brain_decision(test_prompt)

        print(f"Raw decision: {raw_decision}")

        # Test the parsing logic manually
        if "Action: Control Lights" in raw_decision or "control lights" in raw_decision.lower():
            print("✅ Brain correctly identified lights control")

            # Test the lights brain directly
            from llm_response.smart_lights_brain import process_lights_command
            lights_result = await process_lights_command("turn off closet light")

            result = {
                'action': 'lights_control',
                'success': lights_result.get('success', False),
                'lights_result': lights_result,
                'raw_decision': raw_decision
            }
        else:
            result = {
                'action': 'unknown',
                'success': False,
                'raw_decision': raw_decision
            }
        
        print("Brain processing result:", result)
        
        action = result.get('action', 'unknown')
        if action == 'lights_control':
            print("✅ Brain correctly identified and processed lights control")
            if result.get('success'):
                print("✅ Lights command executed successfully")
                lights_result = result.get('lights_result', {})
                print(f"Lights result: {lights_result}")
                
                # Turn the light back on to clean up
                print("Turning light back on for cleanup...")
                cleanup_result = await process_lights_command("turn on closet light")
                if cleanup_result.get('success'):
                    print("✅ Cleanup successful - light turned back on")
                else:
                    print("❌ Cleanup failed")
                    
            else:
                print("❌ Lights command failed")
                lights_result = result.get('lights_result', {})
                print(f"Failure details: {lights_result}")
        elif action == 'lights_error':
            print("❌ Lights error occurred")
            print(f"Error: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ Brain did not identify lights control (action: {action})")
            print(f"Raw decision: {result.get('raw_decision', 'No raw decision')}")
        
        return action == 'lights_control' and result.get('success', False)
        
    except Exception as e:
        print(f"❌ Error during full integration testing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the full integration test."""
    print("🔍 Testing Full Brain Integration")
    print("=" * 50)
    
    success = await test_full_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Full brain integration working perfectly!")
        print("💡 Luna can now control lights via Discord commands!")
    else:
        print("❌ Full brain integration failed!")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

import os
import logging

# --- API & Model Configuration ---
LM_STUDIO_URL = os.environ.get("LM_STUDIO_URL", "http://127.0.0.1:1234/v1")
LM_STUDIO_API_KEY = os.environ.get("LM_STUDIO_API_KEY", "lm-studio-key")
LM_STUDIO_MODEL_NAME = os.environ.get("LM_STUDIO_MODEL_NAME", "gemma-3-12b-it")  # Model for decisions, query rewriting, etc.

# --- LLAMA.CPP Configuration (replaced Ollama) ---
LLAMA_CPP_MODEL_PATH = os.environ.get("LLAMA_CPP_MODEL_PATH", "./models/luna-model.gguf")  # Path to GGUF model file
# Gemma 3 decision model for Vulkan offload (RX6650XT)
GEMMA_CPP_MODEL_PATH = os.environ.get(
    "GEMMA_CPP_MODEL_PATH",
    r"C:/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-4B-GGUF"
)
# "C:/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-4B-GGUF/Qwen3-4B-Q4_K_M.gguf"
_config_logger = logging.getLogger(__name__)
# Resolve directory to actual .gguf file if necessary
if os.path.isdir(GEMMA_CPP_MODEL_PATH):
    try:
        files = os.listdir(GEMMA_CPP_MODEL_PATH)
        gguf_files = [f for f in files if f.lower().endswith('.gguf')]
        if gguf_files:
            old_path = GEMMA_CPP_MODEL_PATH
            GEMMA_CPP_MODEL_PATH = os.path.join(GEMMA_CPP_MODEL_PATH, gguf_files[0])
            _config_logger.info(f"Resolved GEMMA_CPP_MODEL_PATH from directory {old_path} to file {GEMMA_CPP_MODEL_PATH}")
        else:
            _config_logger.error(f"No .gguf file found in model directory: {GEMMA_CPP_MODEL_PATH}")
            # Keep the directory path as fallback
    except (OSError, IndexError) as e:
        _config_logger.error(f"Error resolving GEMMA_CPP_MODEL_PATH: {e}")
        # Keep the directory path as fallback
LLAMA_CPP_N_CTX = int(os.environ.get("LLAMA_CPP_N_CTX", "8096"))  # Context length
LLAMA_CPP_N_THREADS = int(os.environ.get("LLAMA_CPP_N_THREADS", "-1"))  # Number of threads (-1 for auto)
LLAMA_CPP_N_GPU_LAYERS = int(os.environ.get("LLAMA_CPP_N_GPU_LAYERS", "35"))  # GPU layers (34 layers + embedding)
LLAMA_CPP_USE_MMAP = os.environ.get("LLAMA_CPP_USE_MMAP", "true").lower() == "true"
LLAMA_CPP_USE_MLOCK = os.environ.get("LLAMA_CPP_USE_MLOCK", "true").lower() == "true"
LLAMA_CPP_VERBOSE = os.environ.get("LLAMA_CPP_VERBOSE", "false").lower() == "true"

# Legacy names for compatibility (will be replaced throughout codebase)
OLLAMA_MODEL_NAME = "luna-tune:latest"  # Used for logging/identification only
OLLAMA_MEMORY_MODEL = "luna-memory"  # Memory model identifier

KOKORO_BASE_URL = os.getenv("KOKORO_FASTAPI_URL", "http://127.0.0.1:8880") + "/v1"
DECISION_MODEL = "gemma-3-4b-it-qat"  # Model for decision making
REWRITE_MODEL = "gemma-3-4b-it-qat" # Model for RAG query rewriting

# LM Studio Vision (Optional)
LM_STUDIO_VISION_URL = os.environ.get("LM_STUDIO_VISION_URL")
LM_STUDIO_VISION_API_KEY = os.environ.get("LM_STUDIO_VISION_API_KEY", "lm-studio-vision-key")
VISION_MODEL = os.environ.get("LM_STUDIO_VISION_MODEL")

# --- Channel IDs ---
TRANSCRIPT_LOG_CHANNEL_ID = 1369158552996024370
CALL_NOTIFICATION_CHANNEL_ID_STR = os.getenv("CALL_NOTIFICATION_CHANNEL_ID")
PROMPT_LOG_CHANNEL_ID = 1369178090995187783

# --- VTube Studio Configuration ---
VTUBE_STUDIO_ENABLED = os.getenv("VTUBE_STUDIO_ENABLED", "false").lower() in ("true", "1", "yes")
VTUBE_STUDIO_URL = os.getenv("VTUBE_STUDIO_URL", "ws://127.0.0.1:8001")
VTUBE_STUDIO_PLUGIN_NAME = "LunaDiscordBot"
VTUBE_STUDIO_PLUGIN_DEVELOPER = "Luna"
VTUBE_STUDIO_AUDIO_DEVICE = os.getenv("VTUBE_STUDIO_AUDIO_DEVICE", "CABLE Input")

# --- Database ---
DB_PATH = "conversation_logs.db" # Define DB Path globally (Consider making this configurable)

# --- Command Settings ---
SCREENSHOT_COOLDOWN = 1  # seconds

# --- Automatic Screenshot Settings ---
# Background awareness feature - Luna takes screenshots periodically to understand context
AUTO_SCREENSHOT_ENABLED = os.getenv("AUTO_SCREENSHOT_ENABLED", "false").lower() in ("true", "1", "yes")
AUTO_SCREENSHOT_INTERVAL = float(os.environ.get("AUTO_SCREENSHOT_INTERVAL", "25.0"))  # seconds
AUTO_SCREENSHOT_MAX_STORAGE = int(os.environ.get("AUTO_SCREENSHOT_MAX_STORAGE", "5"))  # max background observations to keep

# --- Voice Response Settings ---
# Controls how Luna responds to voice conversations
# GROUP_SILENCE_THRESHOLD: How long Luna waits after the last person speaks before analyzing
# the conversation to decide if she should respond. Longer values make her less "chatty".
# Can be set via environment variable: GROUP_SILENCE_THRESHOLD=5.0
GROUP_SILENCE_THRESHOLD = float(os.environ.get("GROUP_SILENCE_THRESHOLD", "0.5"))  # seconds

# --- Correct Gemma 3 4B Conversation Formatting ---
# Using official Gemma conversation format for proper conversation history handling

# Gemma conversation turns - these are the official control tokens
PROMPT_USER_START = "<start_of_turn>user"
PROMPT_USER_END = "<end_of_turn>"
PROMPT_MODEL_START = "<start_of_turn>model"  
PROMPT_MODEL_END = "<end_of_turn>"

# Context headers (simple, token-efficient)
PROMPT_MEMORY_CONTEXT_HEADER = ""
PROMPT_SCREEN_CONTEXT_HEADER = ""
PROMPT_IMAGE_ANALYSIS_HEADER = ""
PROMPT_BRAIN_CONTEXT_HEADER = ""

# --- User Profile Mapping ---
# Maps Discord User IDs to user profiles
# Each profile contains a 'main_name' (how Luna refers to them)
# and a list of 'aliases' (other names they might use, MUST be lowercase)
USER_PROFILES = {
    443964602850738176: { # Tachi / Sir Goonlord / Goonlord / Guy
        "main_name": "Tachi",
        "aliases": ["tachi", "sir goonlord", "goonlord",], # Added 'guy' and 'gavin'
        "volume_multiplier": 1.0  
    },
     202846383361884160: { # Ethan (Placeholder ID - REMOVE or REPLACE)
         "main_name": "Ethan",
         "aliases": ["ethan", "UwU"],
         "volume_multiplier": 1.0  
     },
    464688859486224394: { # Toast / Mari
        "main_name": "Mari", # Assuming Mari is the preferred main name
        "aliases": ["toast", "mari", "marty"],
        "volume_multiplier": 1.0  
    },
    443182881737670666: { # Jaden / Jayden
        "main_name": "Jaden",
        "aliases": ["jaden", "jayden"],
        "volume_multiplier": 1.0  
    },
    297176773140152321: { # Zack / Zach
        "main_name": "Zack",
        "aliases": ["zack", "zach"],
        "volume_multiplier": 1.0  
    },
    750136646074105938: { # Live / Liv
        "main_name": "Liv",
        "aliases": ["live", "liv"],
        "volume_multiplier": 1.0  
    },
    921637353364287489: { # Live / Liv
        "main_name": "Gavin",
        "aliases": ["test", "gavin"],
        "volume_multiplier": 1.7  
    },
    745467055607644221: { # Live / Liv
        "main_name": "Max",
        "aliases": ["Max", "Maxforce12"],
        "volume_multiplier": 1.0  
    },
    1060678701739683850: { # Live / Liv
        "main_name": "Pool",
        "aliases": ["Pool", "pool pisser"],
        "volume_multiplier": 1.0  
    },
    778803644962439178: { # Live / Liv
        "main_name": "Rain",
        "aliases": ["rain", "Rain"],
        "volume_multiplier": 1.0  
    },
    840483997658382336: { # Live / Liv
        "main_name": "North",
        "aliases": ["north", "North"],
        "volume_multiplier": 1.0  
    },
    207335033764052994: { # Live / Liv
        "main_name": "LMK",
        "aliases": ["LMK", "lily", "lilly",],
        "volume_multiplier": 1.0  
    },
}

# --- Helper Functions for User Profiles ---

def get_main_name_from_alias(alias: str) -> str | None:
    """
    Finds the main name associated with a given alias.
    Returns the original alias if no profile is found.
    """
    alias_lower = alias.lower()
    for _user_id, profile in USER_PROFILES.items():
        if alias_lower in profile.get("aliases", []):
            return profile.get("main_name")
    # If no alias matches, return the original alias capitalized
    # This handles cases where a new person joins or someone uses an unlisted name
    # If no alias matches, return the original alias capitalized
    # This handles cases where a new person joins or someone uses an unlisted name
    return alias.capitalize()

def get_id_from_alias(alias: str) -> int | None:
    """Finds the Discord User ID associated with a given alias."""
    alias_lower = alias.lower()
    for user_id, profile in USER_PROFILES.items():
        if alias_lower in profile.get("aliases", []):
            return user_id
    return None

def get_profile_by_id(user_id: int) -> dict | None:
    """Gets the user profile dictionary by Discord User ID."""
    return USER_PROFILES.get(user_id)

def get_main_name_by_id(user_id: int) -> str | None:
    """
    Gets the main name directly by Discord User ID.
    Returns None if the ID is not found.
    """
    profile = USER_PROFILES.get(user_id)
    if profile:
        return profile.get("main_name")
    return None

def get_volume_multiplier_by_id(user_id: int) -> float:
    """
    Gets the volume multiplier for a specific user by Discord User ID.
    Returns 1.0 (no adjustment) if the user is not found or has no volume setting.

    Args:
        user_id (int): Discord User ID

    Returns:
        float: Volume multiplier (1.0 = no change, 2.0 = double volume, 0.5 = half volume)
    """
    profile = USER_PROFILES.get(user_id)
    if profile:
        return profile.get("volume_multiplier", 1.0)
    return 1.0

def set_volume_multiplier_by_id(user_id: int, multiplier: float) -> bool:
    """
    Sets the volume multiplier for a specific user by Discord User ID.
    Persists the change to the config file.

    Args:
        user_id (int): Discord User ID
        multiplier (float): Volume multiplier (1.0 = no change, 2.0 = double volume, etc.)

    Returns:
        bool: True if successful, False if user not found
    """
    if user_id in USER_PROFILES:
        USER_PROFILES[user_id]["volume_multiplier"] = multiplier
        # Persist the change to the config file
        _save_config_to_file()
        return True
    return False

def _save_config_to_file():
    """
    Saves the current USER_PROFILES to the config file.
    This ensures volume multiplier changes persist across bot restarts.
    """
    import inspect
    import re

    try:
        # Get the path to this config file
        config_file_path = inspect.getfile(inspect.currentframe())

        # Read the current file content
        with open(config_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Generate the new USER_PROFILES string
        profiles_str = _generate_user_profiles_string()

        # Replace the USER_PROFILES section in the file
        # Find the start and end of the USER_PROFILES dictionary
        pattern = r'(USER_PROFILES\s*=\s*\{)(.*?)(\n\})'

        def replace_profiles(match):
            return f"{match.group(1)}{profiles_str}{match.group(3)}"

        new_content = re.sub(pattern, replace_profiles, content, flags=re.DOTALL)

        # Write the updated content back to the file
        with open(config_file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)

        print(f"✅ Config file updated successfully")

    except Exception as e:
        print(f"❌ Error saving config file: {e}")
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to save config file: {e}", exc_info=True)

def _generate_user_profiles_string() -> str:
    """
    Generates the formatted string representation of USER_PROFILES for the config file.
    """
    lines = []

    for user_id, profile in USER_PROFILES.items():
        # Get the comment from the original if it exists
        main_name = profile.get("main_name", "Unknown")
        aliases = profile.get("aliases", [])
        volume_multiplier = profile.get("volume_multiplier", 1.0)

        # Format aliases list
        aliases_str = "[" + ", ".join([f'"{alias}"' for alias in aliases]) + "]"

        # Create the profile entry
        lines.append(f"    {user_id}: {{  # {main_name}")
        lines.append(f'        "main_name": "{main_name}",')
        lines.append(f'        "aliases": {aliases_str},')
        lines.append(f'        "volume_multiplier": {volume_multiplier}  # Volume adjustment')
        lines.append("    },")

    return "\n" + "\n".join(lines) + "\n"

def list_volume_settings() -> dict:
    """
    Returns a dictionary of all users and their volume multipliers.

    Returns:
        dict: {user_id: {"main_name": str, "volume_multiplier": float}}
    """
    result = {}
    for user_id, profile in USER_PROFILES.items():
        result[user_id] = {
            "main_name": profile.get("main_name", "Unknown"),
            "volume_multiplier": profile.get("volume_multiplier", 1.0)
        }
    return result

# --- Deprecated User Alias Mapping ---
# USER_ALIASES = { ... } # Kept for reference, but functionally replaced by USER_PROFILES

# --- Conversation History Settings ---
MAX_HISTORY_MESSAGES = 15 # Full conversation history for LLM context
MAX_STORED_MESSAGES = 15 # Maximum messages to keep in session storage
RAG_TOP_K = 50 # How many documents to retrieve initially
RAG_CONTEXT_COUNT = 5 # Reduced from 15 to 5 for faster TTFT - fewer retrieved documents in final prompt

# --- Response Generation ---
DEFAULT_TEMP = 1.5  # Restored Luna's original personality temperature
TEMP_PARTICIPATION_INCREMENT = 0.1
MAX_TEMP = 1.8 # Restored higher max temperature for Luna's personality
MAX_OUTPUT_TOKENS = 300  # Restored Luna's original response length limit
TOP_P = 0.85

# --- TTFT Optimization Settings ---
# Reduce these values if experiencing high TTFT (Time-to-First-Token)
OPTIMIZE_FOR_SPEED = True  # Set to True to prioritize speed over context
MAX_CONTEXT_CHARS = 4000   # Maximum characters in context to prevent TTFT spikes
FAST_SESSION_LIMIT = 30    # Full session messages for complete conversation history
FAST_RAG_LIMIT = 2         # Reduced RAG context for faster inference

# --- DM Generation ---
DM_MAX_OUTPUT_TOKENS = 150
DM_TEMP = 0.85

# --- Call Message Generation ---
CALL_MSG_MAX_OUTPUT_TOKENS = 50
CALL_MSG_TEMP = 0.75

# --- Image Analysis ---
IMAGE_ANALYSIS_MAX_TOKENS = 150
IMAGE_ANALYSIS_TEMP = 0.2
IMAGE_DOWNLOAD_TIMEOUT = 10.0

# --- Query Rewriting ---
REWRITE_MAX_TOKENS = 50
REWRITE_TEMP = 0.7

# --- Minecraft Integration Toggle ---
MINECRAFT_MODE_ENABLED = False  # Set to True to enable Minecraft integration

def is_minecraft_mode_enabled() -> bool:
    """
    Returns whether Minecraft integration is enabled.
    """
    return MINECRAFT_MODE_ENABLED

def set_minecraft_mode_enabled(enabled: bool) -> None:
    """
    Sets the Minecraft integration mode and persists it to the config file.
    """
    global MINECRAFT_MODE_ENABLED
    MINECRAFT_MODE_ENABLED = enabled
    _save_minecraft_mode_to_file()

def _save_minecraft_mode_to_file():
    """
    Saves the current MINECRAFT_MODE_ENABLED value to the config file.
    """
    import inspect
    import re
    try:
        config_file_path = inspect.getfile(inspect.currentframe())
        with open(config_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # Replace the MINECRAFT_MODE_ENABLED line
        new_content = re.sub(
            r'MINECRAFT_MODE_ENABLED\s*=\s*(True|False)',
            f'MINECRAFT_MODE_ENABLED = {MINECRAFT_MODE_ENABLED}',
            content
        )
        with open(config_file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ Minecraft mode config updated successfully")
    except Exception as e:
        print(f"❌ Error saving Minecraft mode config: {e}")
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to save Minecraft mode config: {e}", exc_info=True)

# --- Hallucination Filter for Speech-to-Text ---
HALLUCINATION_FILTER_PHRASES = [
    "thank you",
    "you",
    "thank you.",
    "you.",
    "bye",
    "bye.",
    
    # Add more phrases as needed
]

def add_hallucination_phrase(phrase: str) -> bool:
    """Add a phrase to the hallucination filter and persist it."""
    phrase = phrase.strip().lower()
    if phrase and phrase not in HALLUCINATION_FILTER_PHRASES:
        HALLUCINATION_FILTER_PHRASES.append(phrase)
        _save_hallucination_phrases_to_file()
        return True
    return False

def remove_hallucination_phrase(phrase: str) -> bool:
    """Remove a phrase from the hallucination filter and persist it."""
    phrase = phrase.strip().lower()
    if phrase in HALLUCINATION_FILTER_PHRASES:
        HALLUCINATION_FILTER_PHRASES.remove(phrase)
        _save_hallucination_phrases_to_file()
        return True
    return False

def _save_hallucination_phrases_to_file():
    """Persist the HALLUCINATION_FILTER_PHRASES list to this config file."""
    import inspect
    import re
    try:
        config_file_path = inspect.getfile(inspect.currentframe())
        with open(config_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # Replace the HALLUCINATION_FILTER_PHRASES section
        pattern = r'(HALLUCINATION_FILTER_PHRASES\s*=\s*\[)(.*?)(\])'
        phrases_str = '\n    ' + ',\n    '.join(f'"{p}"' for p in HALLUCINATION_FILTER_PHRASES) + '\n'
        def replace_phrases(match):
            return f"{match.group(1)}{phrases_str}{match.group(3)}"
        new_content = re.sub(pattern, replace_phrases, content, flags=re.DOTALL)
        with open(config_file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ Hallucination filter updated successfully")
    except Exception as e:
        print(f"❌ Error saving hallucination filter: {e}")
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to save hallucination filter: {e}", exc_info=True)

# --- Decision-Making Performance Settings ---
DECISION_MAX_TOKENS = 50  # Increased from 20 to allow complete decision responses
DECISION_TEMP = 0.5
DECISION_CONFIDENCE_THRESHOLD = 85  # Minimum confidence percentage for Luna to respond

# TTFT Optimization for Decisions
FAST_DECISION_MODE = True           # Enable optimized decision processing
DECISION_TIMEOUT = 3.0              # Timeout for decision API calls in seconds  
DECISION_MAX_CONTEXT_MESSAGES = 3   # Reduced from ~5-10 to 3 for faster processing
DECISION_MAX_SPEAKER_HISTORY = 3    # Limit speaker turn analysis to last 3 turns

# --- Ollama TTFT Optimization Settings ---
OLLAMA_KEEP_ALIVE = "30m"              # Keep model loaded for 30 minutes
OLLAMA_NUM_PREDICT = 300               # Match max_tokens to reduce processing
OLLAMA_NUM_THREAD = 0                  # Auto-detect optimal threads
OLLAMA_LOW_VRAM = False                # Set to True if running out of VRAM
OLLAMA_REQUEST_TIMEOUT = 300           # 5 minutes for long requests
OLLAMA_PRELOAD_MODEL = True            # Preload model on startup
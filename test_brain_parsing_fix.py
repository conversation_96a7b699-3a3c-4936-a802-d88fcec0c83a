#!/usr/bin/env python3
"""
Test Brain Parsing Fix
=====================

Test that the brain parsing correctly identifies lights control and doesn't
misinterpret it as a call action.
"""

import asyncio
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_brain_parsing():
    """Test the brain parsing logic."""
    print("Testing brain parsing fix...")
    
    try:
        import llm_brain
        
        # Test the brain decision
        test_prompt = 'User <PERSON> said: "Luna turn off my closet light" in channel #luna-commands'
        raw_decision = await llm_brain.get_brain_decision(test_prompt)
        
        print("Raw brain decision:", repr(raw_decision))
        
        # Check if it contains the correct Action: statement
        if "Action: Control Lights" in raw_decision:
            print("✅ Brain correctly identified 'Action: Control Lights'")
        elif "control lights" in raw_decision.lower():
            print("✅ Brain identified lights control (case insensitive)")
        else:
            print("❌ Brain did not identify lights control")
            return False
        
        # Check that it doesn't contain call-related text that would be misinterpreted
        problematic_phrases = [
            "call user",
            "action: call",
            "brain decided to call"
        ]
        
        has_call_issues = any(phrase in raw_decision.lower() for phrase in problematic_phrases)
        if has_call_issues:
            print("❌ Brain response contains call-related text that could be misinterpreted")
            return False
        else:
            print("✅ Brain response doesn't contain problematic call-related text")
        
        # Test the parsing logic by simulating what main.py would do
        decision_lower = raw_decision.lower()
        
        # Check if the old problematic regex would match
        import re
        old_call_match = re.search(r"call\s+(.*)", decision_lower)
        if old_call_match:
            print(f"⚠️ Old regex would have matched: '{old_call_match.group(1).strip()}'")
            print("But this should now be ignored due to the fix in main.py")
        else:
            print("✅ Old problematic regex doesn't match")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during brain parsing test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the brain parsing test."""
    print("🔍 Testing Brain Parsing Fix")
    print("=" * 50)
    
    success = await test_brain_parsing()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Brain parsing fix working correctly!")
        print("💡 Luna should now properly detect lights control without call conflicts!")
    else:
        print("❌ Brain parsing fix failed!")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

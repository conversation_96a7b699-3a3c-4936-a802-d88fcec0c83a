#!/usr/bin/env python3
"""
Smart Lights Brain Module for Luna Discord Bot
==============================================

This module handles LLM-based processing of light control commands using the same
Qwen3 4B model as the main brain system. It uses function calling to detect and
execute light control commands with natural language processing.

Features:
- Natural language light command detection
- Function calling with LIFX bulb control
- GPU isolation using RX 6650XT (Device 1)
- Integration with Luna's brain system
"""

import asyncio
import json
import logging
import os
import re
import threading
import time
from typing import Dict, Any, Optional, List
from llama_cpp import Llama

# Import necessary components from llm_response package
from .config import (
    GEMMA_CPP_MODEL_PATH,
    LLAMA_CPP_N_CTX,
    LLAMA_CPP_N_THREADS,
)
from .gpu_isolation import isolated_gpu_access, get_safe_llama_params, register_model
from .smart_lights import (
    LIGHT_FUNCTIONS,
    LIGHT_FUNCTION_MAP,
    execute_light_function,
    get_available_bulbs,
    is_light_related_command
)

# Import latency tracking
try:
    from .latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracking isn't available
    def mark_latency_timestamp(event_name):
        pass

# Setup logger
logger = logging.getLogger(__name__)

# Configuration
TEMPERATURE = 0.3  # Lower temperature for more consistent function calling
MAX_TOKENS = 150   # Sufficient for function calls

# Cache for the system prompt
_lights_brain_system_prompt = None

# Cache for the lights brain model client
_lights_brain_client = None
_lights_brain_lock = threading.Lock()
_lights_brain_inference_lock = asyncio.Lock()  # Async lock for inference

def get_lights_brain_client():
    """Initialize and return a cached llama.cpp client for the lights brain model using RX 6650XT (Device 1)."""
    global _lights_brain_client
    if _lights_brain_client is None:
        with _lights_brain_lock:
            if _lights_brain_client is None:
                # Ensure model path exists
                if not os.path.exists(GEMMA_CPP_MODEL_PATH):
                    logger.error(f"Lights brain model not found at: {GEMMA_CPP_MODEL_PATH}")
                    raise FileNotFoundError(f"Lights brain model not found at: {GEMMA_CPP_MODEL_PATH}")

                logger.info("Initializing lights brain model with GPU isolation...")

                # Use isolated GPU access for AMD GPU (device 1)
                with isolated_gpu_access(1, "lights-brain"):
                    # Get safe parameters for AMD GPU
                    safe_params = get_safe_llama_params(
                        gpu_id=1,
                        model_path=GEMMA_CPP_MODEL_PATH,
                        n_ctx=LLAMA_CPP_N_CTX,
                        n_threads=LLAMA_CPP_N_THREADS
                    )

                    # Create and cache the client
                    _lights_brain_client = Llama(**safe_params)

                    # Register the model
                    register_model(1, "lights-brain")

                logger.info("✅ Lights brain llama.cpp client initialized successfully!")
                logger.info(f"Lights brain model loaded: {GEMMA_CPP_MODEL_PATH}")
                logger.info("GPU: RX 6650XT (Device 1) - Isolated configuration")
    return _lights_brain_client

def _load_lights_brain_system_prompt():
    """Loads the system prompt from smart_lights_prompt.txt"""
    global _lights_brain_system_prompt
    if _lights_brain_system_prompt is None:
        try:
            with open('smart_lights_prompt.txt', 'r', encoding='utf-8') as f:
                _lights_brain_system_prompt = f.read()
            logger.info("Smart lights brain system prompt loaded successfully.")
        except FileNotFoundError:
            logger.error("smart_lights_prompt.txt not found. Using default fallback.")
            # Create a basic fallback prompt
            available_bulbs = get_available_bulbs()
            bulbs_list = ", ".join(available_bulbs) if available_bulbs else "No bulbs discovered"
            _lights_brain_system_prompt = f"""You are Luna's smart lights control system. You can control LIFX bulbs using function calls.

Available bulbs: {bulbs_list}

For light control commands, respond ONLY with a JSON function call. Use these functions:
- turn_on(name): Turn on a bulb
- turn_off(name): Turn off a bulb  
- set_color(name, color, brightness): Set bulb color and brightness

Examples:
- "turn on closet light" → {{"function": "turn_on", "arguments": {{"name": "Closet Light"}}}}
- "make bedroom light red" → {{"function": "set_color", "arguments": {{"name": "Bedroom Light", "color": {{"r": 255, "g": 0, "b": 0}}, "brightness": 100}}}}

Always match bulb names from the available list above."""
        except Exception as e:
            logger.error(f"Error loading smart_lights_prompt.txt: {e}. Using default fallback.")
            _lights_brain_system_prompt = "You are Luna's smart lights control system. Control LIFX bulbs using function calls."
    return _lights_brain_system_prompt

async def get_lights_brain_decision(prompt: str) -> str:
    """
    Get a decision from the lights brain model.

    Args:
        prompt: The user prompt to process

    Returns:
        Raw decision text from the model
    """
    # Prepare the system and user prompt
    system_prompt_content = _load_lights_brain_system_prompt()
    full_prompt = f"System: {system_prompt_content}\n\nUser: {prompt}\n\nAssistant:"

    # Use cached lights brain llama.cpp client to run inference
    mark_latency_timestamp("lights_brain_api_start")
    model = get_lights_brain_client()

    # Use async lock to prevent concurrent access to the same model instance
    async with _lights_brain_inference_lock:
        loop = asyncio.get_event_loop()
        def run_inference():
            return model(
                full_prompt,
                temperature=TEMPERATURE,
                max_tokens=MAX_TOKENS,
                top_p=0.9,
                stop=["\n\n", "User:", "System:"]
            )

        result = await loop.run_in_executor(None, run_inference)

    mark_latency_timestamp("lights_brain_api_end")

    # Extract decision text from llama.cpp result
    decision = result.get('choices', [{}])[0].get('text', '').strip()
    logger.info(f"Lights Brain Decision (llama.cpp): {decision}")
    return decision

def parse_lights_function_call(response_text: str) -> tuple[Optional[str], Optional[Dict[str, Any]]]:
    """
    Parse function call from the lights brain response text.

    Args:
        response_text: The raw text response from the model

    Returns:
        Tuple of (function_name, arguments) or (None, None) if no function call
    """
    try:
        # Remove any think tags or extra whitespace
        content = re.sub(r"</?think.*?>", "", response_text, flags=re.DOTALL | re.IGNORECASE).strip()

        # Look for JSON function call
        json_match = re.search(r"(\{.*\})", content, flags=re.DOTALL)
        if json_match:
            try:
                func_call = json.loads(json_match.group(1))
                if "function" in func_call and "arguments" in func_call:
                    return func_call["function"], func_call["arguments"]
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON from response: {e}")

        # If no JSON found, try to extract function name from simple format
        # Look for patterns like "turn_on" or "set_color"
        for func_name in LIGHT_FUNCTION_MAP.keys():
            if func_name in content.lower():
                logger.info(f"Found function name '{func_name}' in response, but no proper JSON format")
                # Try to extract basic arguments based on the function
                if func_name in ["turn_on", "turn_off"]:
                    # Look for bulb name in the original response
                    return func_name, {"name": "Closet Light"}  # Default for testing
                elif func_name == "set_color":
                    return func_name, {
                        "name": "Closet Light",
                        "color": {"r": 255, "g": 0, "b": 0},
                        "brightness": 100
                    }

        return None, None
    except Exception as e:
        logger.error(f"Error parsing lights function call: {e}")
        return None, None

async def process_lights_command(prompt: str) -> Dict[str, Any]:
    """
    Process a light control command using the lights brain.
    
    Args:
        prompt: The user command to process
        
    Returns:
        Dictionary with action result or error information
    """
    logger.info(f"[LIGHTS_BRAIN] Processing command: {prompt}")
    
    # Check if this is actually a light-related command
    if not is_light_related_command(prompt):
        return {"action": "not_lights", "details": "Command not related to lights"}
    
    try:
        # Get decision from lights brain
        response_text = await get_lights_brain_decision(prompt)

        # Parse function call
        function_name, arguments = parse_lights_function_call(response_text)
        
        if function_name and arguments:
            logger.info(f"[LIGHTS_BRAIN] Executing function: {function_name} with args: {arguments}")
            
            # Execute the light function
            result = execute_light_function(function_name, **arguments)
            
            return {
                "action": "lights_control",
                "function": function_name,
                "arguments": arguments,
                "result": result,
                "success": "error" not in result
            }
        else:
            logger.warning(f"[LIGHTS_BRAIN] No valid function call found in response: {response_text}")
            return {
                "action": "lights_parse_error",
                "details": "Could not parse light control command",
                "raw_response": response_text
            }
    
    except Exception as e:
        logger.error(f"[LIGHTS_BRAIN] Error processing lights command: {e}")
        return {
            "action": "lights_error",
            "details": f"Error processing lights command: {e}"
        }

# Test function for development
if __name__ == "__main__":
    async def run_tests():
        print("Testing Smart Lights Brain...")
        
        test_commands = [
            "Luna turn on my closet light",
            "turn off the bedroom light",
            "make the living room light red at 50% brightness",
            "set kitchen light to blue"
        ]
        
        for cmd in test_commands:
            print(f"\nTest Command: {cmd}")
            result = await process_lights_command(cmd)
            print(f"Result: {result}")
    
    # Run the async test function
    asyncio.run(run_tests())

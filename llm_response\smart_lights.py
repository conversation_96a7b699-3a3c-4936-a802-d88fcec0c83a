#!/usr/bin/env python3
"""
Smart Lights Module for Luna Discord Bot
========================================

This module handles LIFX smart bulb discovery, control functions, and integration
with Luna's LLM brain system for natural language light control.

Features:
- LIFX bulb discovery and caching
- Turn on/off functionality
- Color and brightness control
- Natural language command processing
- Integration with <PERSON>'s brain system
"""

import json
import logging
import re
import sys
import threading
import time
from typing import Dict, Any, Optional, List
from lifxlan import LifxLAN

# Setup logger
logger = logging.getLogger(__name__)

# Global variables for bulb management
_bulbs_cache: Dict[str, Any] = {}
_bulbs_cache_lock = threading.Lock()
_last_discovery_time = 0
_discovery_cache_duration = 300  # 5 minutes cache

# Predefined colors (RGB values)
PREDEFINED_COLORS = {
    "red": {"r": 255, "g": 0, "b": 0},
    "green": {"r": 0, "g": 255, "b": 0},
    "blue": {"r": 0, "g": 0, "b": 255},
    "yellow": {"r": 255, "g": 255, "b": 0},
    "purple": {"r": 128, "g": 0, "b": 128},
    "pink": {"r": 255, "g": 192, "b": 203},
    "orange": {"r": 255, "g": 165, "b": 0},
    "white": {"r": 255, "g": 255, "b": 255},
    "warm_white": {"r": 255, "g": 244, "b": 229},
    "cool_white": {"r": 248, "g": 248, "b": 255},
    "cyan": {"r": 0, "g": 255, "b": 255},
    "magenta": {"r": 255, "g": 0, "b": 255},
}

def discover_bulbs(force_refresh: bool = False) -> Dict[str, Any]:
    """
    Discover LIFX bulbs on the network with caching.
    
    Args:
        force_refresh: If True, force a new discovery even if cache is valid
        
    Returns:
        Dictionary mapping bulb labels to bulb objects
    """
    global _bulbs_cache, _last_discovery_time
    
    current_time = time.time()
    
    with _bulbs_cache_lock:
        # Return cached results if still valid and not forcing refresh
        if (not force_refresh and 
            _bulbs_cache and 
            (current_time - _last_discovery_time) < _discovery_cache_duration):
            logger.debug(f"Using cached bulb discovery ({len(_bulbs_cache)} bulbs)")
            return _bulbs_cache.copy()
        
        # Perform new discovery
        logger.info("Discovering LIFX bulbs on network...")
        try:
            lan = LifxLAN(3)  # Discover up to 3 bulbs
            discovered_lights = lan.get_lights()
            
            # Build bulb dictionary with labels as keys
            new_bulbs = {}
            for bulb in discovered_lights:
                try:
                    label = bulb.get_label()
                    new_bulbs[label] = bulb
                    logger.info(f"Discovered bulb: {label}")
                except Exception as e:
                    logger.warning(f"Failed to get label for bulb: {e}")
            
            # Update cache
            _bulbs_cache = new_bulbs
            _last_discovery_time = current_time
            
            logger.info(f"Discovery complete. Found {len(_bulbs_cache)} bulbs: {list(_bulbs_cache.keys())}")
            return _bulbs_cache.copy()
            
        except Exception as e:
            logger.error(f"Error discovering bulbs: {e}")
            # Return empty dict on error, but keep existing cache if available
            if not _bulbs_cache:
                return {}
            return _bulbs_cache.copy()

def get_available_bulbs() -> List[str]:
    """
    Get list of available bulb names.
    
    Returns:
        List of bulb label strings
    """
    bulbs = discover_bulbs()
    return list(bulbs.keys())

def normalize_bulb_name(name: str, available_bulbs: List[str]) -> Optional[str]:
    """
    Normalize and match bulb name against available bulbs.
    Handles case-insensitive matching and common variations.
    
    Args:
        name: The bulb name to normalize
        available_bulbs: List of available bulb names
        
    Returns:
        Matched bulb name or None if no match found
    """
    name_lower = name.lower().strip()
    
    # Direct case-insensitive match
    for bulb_name in available_bulbs:
        if bulb_name.lower() == name_lower:
            return bulb_name
    
    # Partial match (bulb name contains the search term)
    for bulb_name in available_bulbs:
        if name_lower in bulb_name.lower():
            return bulb_name
    
    # Search term contains bulb name
    for bulb_name in available_bulbs:
        if bulb_name.lower() in name_lower:
            return bulb_name
    
    return None

def get_color_by_name(color_name: str) -> Optional[Dict[str, int]]:
    """
    Get RGB color values by color name.

    Args:
        color_name: Name of the color (case-insensitive)

    Returns:
        RGB color dictionary or None if not found
    """
    return PREDEFINED_COLORS.get(color_name.lower().replace(" ", "_"))

def turn_on(name: str) -> Dict[str, Any]:
    """
    Turn on a LIFX bulb.
    
    Args:
        name: Bulb label/name
        
    Returns:
        Dictionary with status or error information
    """
    logger.info(f"[LIGHTS] turn_on called with name='{name}'")
    
    bulbs = discover_bulbs()
    available_bulbs = list(bulbs.keys())
    
    # Normalize the bulb name
    normalized_name = normalize_bulb_name(name, available_bulbs)
    if not normalized_name:
        error_msg = f"No bulb found matching '{name}'. Available: {available_bulbs}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}
    
    bulb = bulbs[normalized_name]
    try:
        bulb.set_power("on")
        success_msg = f"Turned on '{normalized_name}'"
        logger.info(f"[LIGHTS] {success_msg}")
        return {"status": success_msg}
    except Exception as e:
        error_msg = f"Failed to turn on '{normalized_name}': {e}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

def turn_off(name: str) -> Dict[str, Any]:
    """
    Turn off a LIFX bulb.
    
    Args:
        name: Bulb label/name
        
    Returns:
        Dictionary with status or error information
    """
    logger.info(f"[LIGHTS] turn_off called with name='{name}'")
    
    bulbs = discover_bulbs()
    available_bulbs = list(bulbs.keys())
    
    # Normalize the bulb name
    normalized_name = normalize_bulb_name(name, available_bulbs)
    if not normalized_name:
        error_msg = f"No bulb found matching '{name}'. Available: {available_bulbs}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}
    
    bulb = bulbs[normalized_name]
    try:
        bulb.set_power("off")
        success_msg = f"Turned off '{normalized_name}'"
        logger.info(f"[LIGHTS] {success_msg}")
        return {"status": success_msg}
    except Exception as e:
        error_msg = f"Failed to turn off '{normalized_name}': {e}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

def rgb_to_hsbk(r: int, g: int, b: int, brightness: int = 100) -> tuple[int, int, int, int]:
    """
    Convert RGB values to LIFX HSBK format.
    Based on the proven conversion from the Alteryx community example.

    Args:
        r, g, b: RGB values (0-255)
        brightness: Brightness percentage (0-100)

    Returns:
        Tuple of (hue, saturation, brightness, kelvin) in LIFX format (0-65535)
    """
    # Normalize RGB to 0-1 range
    r_norm = r / 255.0
    g_norm = g / 255.0
    b_norm = b / 255.0

    # Find max and min values
    cmax = max(r_norm, g_norm, b_norm)
    cmin = min(r_norm, g_norm, b_norm)
    diff = cmax - cmin

    # Calculate luminance (for HSL-based conversion)
    luminance = (cmax + cmin) / 2.0

    # Calculate saturation
    if luminance > 0.5:
        saturation = diff / (2.0 - cmax - cmin) if (cmax + cmin) != 2.0 else 0
    else:
        saturation = diff / (cmax + cmin) if (cmax + cmin) != 0 else 0

    # Calculate hue
    if diff == 0:
        hue = 0
    elif cmax == r_norm:
        hue = (g_norm - b_norm) / diff
    elif cmax == g_norm:
        hue = 2.0 + (b_norm - r_norm) / diff
    else:  # cmax == b_norm
        hue = 4.0 + (r_norm - g_norm) / diff

    # Convert hue to degrees
    hue = hue * 60
    if hue < 0:
        hue += 360

    # Convert to LIFX format (0-65535)
    h_lifx = int((hue / 360.0) * 65535)
    s_lifx = int(saturation * 65535)
    b_lifx = int((brightness / 100.0) * 65535)
    k_lifx = 3500  # Default kelvin temperature for color bulbs

    # Ensure values are within valid range
    h_lifx = max(0, min(65535, h_lifx))
    s_lifx = max(0, min(65535, s_lifx))
    b_lifx = max(0, min(65535, b_lifx))

    return h_lifx, s_lifx, b_lifx, k_lifx

def set_color(name: str, color: Dict[str, int], brightness: int = 100) -> Dict[str, Any]:
    """
    Set the color and brightness of a LIFX bulb.

    Args:
        name: Bulb label/name
        color: RGB color dictionary with 'r', 'g', 'b' keys (0-255)
        brightness: Brightness percentage (0-100)

    Returns:
        Dictionary with status or error information
    """
    logger.info(f"[LIGHTS] set_color called with name='{name}', color={color}, brightness={brightness}")

    bulbs = discover_bulbs()
    available_bulbs = list(bulbs.keys())

    # Normalize the bulb name
    normalized_name = normalize_bulb_name(name, available_bulbs)
    if not normalized_name:
        error_msg = f"No bulb found matching '{name}'. Available: {available_bulbs}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

    bulb = bulbs[normalized_name]
    try:
        # Convert RGB to HSBK format using the proven conversion
        r, g, b = color.get('r', 0), color.get('g', 0), color.get('b', 0)
        h, s, b_val, k = rgb_to_hsbk(r, g, b, brightness)

        logger.info(f"[LIGHTS] RGB({r},{g},{b}) -> HSBK({h},{s},{b_val},{k})")

        # Set the color on the specific bulb
        bulb.set_color([h, s, b_val, k])
        success_msg = f"Set '{normalized_name}' to RGB({r},{g},{b}) at {brightness}% brightness"
        logger.info(f"[LIGHTS] {success_msg}")
        return {"status": success_msg}
    except Exception as e:
        error_msg = f"Failed to set color for '{normalized_name}': {e}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

# Function schemas for LLM integration
LIGHT_FUNCTIONS = [
    {
        "name": "turn_on",
        "description": "Turn a LIFX smart bulb on",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name or label of the bulb to turn on"
                }
            },
            "required": ["name"]
        }
    },
    {
        "name": "turn_off",
        "description": "Turn a LIFX smart bulb off",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name or label of the bulb to turn off"
                }
            },
            "required": ["name"]
        }
    },
    {
        "name": "set_color",
        "description": "Set the color and brightness of a LIFX smart bulb",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name or label of the bulb"
                },
                "color": {
                    "type": "object",
                    "description": "RGB color values",
                    "properties": {
                        "r": {"type": "integer", "minimum": 0, "maximum": 255, "description": "Red component"},
                        "g": {"type": "integer", "minimum": 0, "maximum": 255, "description": "Green component"},
                        "b": {"type": "integer", "minimum": 0, "maximum": 255, "description": "Blue component"}
                    },
                    "required": ["r", "g", "b"]
                },
                "brightness": {
                    "type": "integer",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "Brightness percentage (0-100)",
                    "default": 100
                }
            },
            "required": ["name", "color"]
        }
    }
]

# Function name to function mapping
LIGHT_FUNCTION_MAP = {
    "turn_on": turn_on,
    "turn_off": turn_off,
    "set_color": set_color
}

def execute_light_function(function_name: str, **kwargs) -> Dict[str, Any]:
    """
    Execute a light control function by name.

    Args:
        function_name: Name of the function to execute
        **kwargs: Function arguments

    Returns:
        Dictionary with result or error information
    """
    if function_name not in LIGHT_FUNCTION_MAP:
        return {"error": f"Unknown light function: {function_name}"}

    try:
        func = LIGHT_FUNCTION_MAP[function_name]
        return func(**kwargs)
    except Exception as e:
        logger.error(f"Error executing light function {function_name}: {e}")
        return {"error": f"Failed to execute {function_name}: {e}"}

def is_light_related_command(text: str) -> bool:
    """
    Check if a text command is related to light control.

    Args:
        text: The text to analyze

    Returns:
        True if the text appears to be a light control command
    """
    light_keywords = [
        'light', 'lights', 'bulb', 'bulbs', 'lamp', 'lamps',
        'turn on', 'turn off', 'switch on', 'switch off',
        'brighten', 'dim', 'brightness', 'color', 'colour',
        'red', 'green', 'blue', 'yellow', 'purple', 'pink',
        'white', 'warm', 'cool', 'lifx'
    ]

    text_lower = text.lower()
    return any(keyword in text_lower for keyword in light_keywords)

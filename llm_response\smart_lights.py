#!/usr/bin/env python3
"""
Smart Lights Module for Luna Discord Bot
========================================

This module handles LIFX smart bulb discovery, control functions, and integration
with Luna's LLM brain system for natural language light control.

Features:
- LIFX bulb discovery and caching
- Turn on/off functionality
- Color and brightness control
- Natural language command processing
- Integration with Luna's brain system
"""

import json
import logging
import re
import sys
import threading
import time
from typing import Dict, Any, Optional, List
from lifxlan import LifxLAN

# Setup logger
logger = logging.getLogger(__name__)

# Global variables for bulb management
_bulbs_cache: Dict[str, Any] = {}
_bulbs_cache_lock = threading.Lock()
_last_discovery_time = 0
_discovery_cache_duration = 300  # 5 minutes cache

def discover_bulbs(force_refresh: bool = False) -> Dict[str, Any]:
    """
    Discover LIFX bulbs on the network with caching.
    
    Args:
        force_refresh: If True, force a new discovery even if cache is valid
        
    Returns:
        Dictionary mapping bulb labels to bulb objects
    """
    global _bulbs_cache, _last_discovery_time
    
    current_time = time.time()
    
    with _bulbs_cache_lock:
        # Return cached results if still valid and not forcing refresh
        if (not force_refresh and 
            _bulbs_cache and 
            (current_time - _last_discovery_time) < _discovery_cache_duration):
            logger.debug(f"Using cached bulb discovery ({len(_bulbs_cache)} bulbs)")
            return _bulbs_cache.copy()
        
        # Perform new discovery
        logger.info("Discovering LIFX bulbs on network...")
        try:
            lan = LifxLAN(3)  # Discover up to 3 bulbs
            discovered_lights = lan.get_lights()
            
            # Build bulb dictionary with labels as keys
            new_bulbs = {}
            for bulb in discovered_lights:
                try:
                    label = bulb.get_label()
                    new_bulbs[label] = bulb
                    logger.info(f"Discovered bulb: {label}")
                except Exception as e:
                    logger.warning(f"Failed to get label for bulb: {e}")
            
            # Update cache
            _bulbs_cache = new_bulbs
            _last_discovery_time = current_time
            
            logger.info(f"Discovery complete. Found {len(_bulbs_cache)} bulbs: {list(_bulbs_cache.keys())}")
            return _bulbs_cache.copy()
            
        except Exception as e:
            logger.error(f"Error discovering bulbs: {e}")
            # Return empty dict on error, but keep existing cache if available
            if not _bulbs_cache:
                return {}
            return _bulbs_cache.copy()

def get_available_bulbs() -> List[str]:
    """
    Get list of available bulb names.
    
    Returns:
        List of bulb label strings
    """
    bulbs = discover_bulbs()
    return list(bulbs.keys())

def normalize_bulb_name(name: str, available_bulbs: List[str]) -> Optional[str]:
    """
    Normalize and match bulb name against available bulbs.
    Handles case-insensitive matching and common variations.
    
    Args:
        name: The bulb name to normalize
        available_bulbs: List of available bulb names
        
    Returns:
        Matched bulb name or None if no match found
    """
    name_lower = name.lower().strip()
    
    # Direct case-insensitive match
    for bulb_name in available_bulbs:
        if bulb_name.lower() == name_lower:
            return bulb_name
    
    # Partial match (bulb name contains the search term)
    for bulb_name in available_bulbs:
        if name_lower in bulb_name.lower():
            return bulb_name
    
    # Search term contains bulb name
    for bulb_name in available_bulbs:
        if bulb_name.lower() in name_lower:
            return bulb_name
    
    return None

def turn_on(name: str) -> Dict[str, Any]:
    """
    Turn on a LIFX bulb.
    
    Args:
        name: Bulb label/name
        
    Returns:
        Dictionary with status or error information
    """
    logger.info(f"[LIGHTS] turn_on called with name='{name}'")
    
    bulbs = discover_bulbs()
    available_bulbs = list(bulbs.keys())
    
    # Normalize the bulb name
    normalized_name = normalize_bulb_name(name, available_bulbs)
    if not normalized_name:
        error_msg = f"No bulb found matching '{name}'. Available: {available_bulbs}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}
    
    bulb = bulbs[normalized_name]
    try:
        bulb.set_power("on")
        success_msg = f"Turned on '{normalized_name}'"
        logger.info(f"[LIGHTS] {success_msg}")
        return {"status": success_msg}
    except Exception as e:
        error_msg = f"Failed to turn on '{normalized_name}': {e}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

def turn_off(name: str) -> Dict[str, Any]:
    """
    Turn off a LIFX bulb.
    
    Args:
        name: Bulb label/name
        
    Returns:
        Dictionary with status or error information
    """
    logger.info(f"[LIGHTS] turn_off called with name='{name}'")
    
    bulbs = discover_bulbs()
    available_bulbs = list(bulbs.keys())
    
    # Normalize the bulb name
    normalized_name = normalize_bulb_name(name, available_bulbs)
    if not normalized_name:
        error_msg = f"No bulb found matching '{name}'. Available: {available_bulbs}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}
    
    bulb = bulbs[normalized_name]
    try:
        bulb.set_power("off")
        success_msg = f"Turned off '{normalized_name}'"
        logger.info(f"[LIGHTS] {success_msg}")
        return {"status": success_msg}
    except Exception as e:
        error_msg = f"Failed to turn off '{normalized_name}': {e}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

def set_color(name: str, color: Dict[str, int], brightness: int = 100) -> Dict[str, Any]:
    """
    Set the color and brightness of a LIFX bulb.

    Args:
        name: Bulb label/name
        color: RGB color dictionary with 'r', 'g', 'b' keys (0-255)
        brightness: Brightness percentage (0-100)

    Returns:
        Dictionary with status or error information
    """
    logger.info(f"[LIGHTS] set_color called with name='{name}', color={color}, brightness={brightness}")

    bulbs = discover_bulbs()
    available_bulbs = list(bulbs.keys())

    # Normalize the bulb name
    normalized_name = normalize_bulb_name(name, available_bulbs)
    if not normalized_name:
        error_msg = f"No bulb found matching '{name}'. Available: {available_bulbs}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

    bulb = bulbs[normalized_name]
    try:
        # Convert RGB to HSBK format for LIFX
        # This is a simplified conversion - LIFX uses HSBK (Hue, Saturation, Brightness, Kelvin)
        r, g, b = color.get('r', 0), color.get('g', 0), color.get('b', 0)

        # Simple RGB to HSB conversion
        r_norm, g_norm, b_norm = r / 255.0, g / 255.0, b / 255.0
        max_val = max(r_norm, g_norm, b_norm)
        min_val = min(r_norm, g_norm, b_norm)
        diff = max_val - min_val

        # Hue calculation
        if diff == 0:
            hue = 0
        elif max_val == r_norm:
            hue = (60 * ((g_norm - b_norm) / diff) + 360) % 360
        elif max_val == g_norm:
            hue = (60 * ((b_norm - r_norm) / diff) + 120) % 360
        else:
            hue = (60 * ((r_norm - g_norm) / diff) + 240) % 360

        # Saturation calculation
        saturation = 0 if max_val == 0 else (diff / max_val)

        # Convert to LIFX format (0-65535)
        h = int((hue / 360) * 65535)
        s = int(saturation * 65535)
        b_val = int((brightness / 100) * 65535)
        k = 3500  # Default kelvin temperature

        bulb.set_color((h, s, b_val, k))
        success_msg = f"Set '{normalized_name}' to {color} at {brightness}% brightness"
        logger.info(f"[LIGHTS] {success_msg}")
        return {"status": success_msg}
    except Exception as e:
        error_msg = f"Failed to set color for '{normalized_name}': {e}"
        logger.error(f"[LIGHTS] {error_msg}")
        return {"error": error_msg}

# Function schemas for LLM integration
LIGHT_FUNCTIONS = [
    {
        "name": "turn_on",
        "description": "Turn a LIFX smart bulb on",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name or label of the bulb to turn on"
                }
            },
            "required": ["name"]
        }
    },
    {
        "name": "turn_off",
        "description": "Turn a LIFX smart bulb off",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name or label of the bulb to turn off"
                }
            },
            "required": ["name"]
        }
    },
    {
        "name": "set_color",
        "description": "Set the color and brightness of a LIFX smart bulb",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name or label of the bulb"
                },
                "color": {
                    "type": "object",
                    "description": "RGB color values",
                    "properties": {
                        "r": {"type": "integer", "minimum": 0, "maximum": 255, "description": "Red component"},
                        "g": {"type": "integer", "minimum": 0, "maximum": 255, "description": "Green component"},
                        "b": {"type": "integer", "minimum": 0, "maximum": 255, "description": "Blue component"}
                    },
                    "required": ["r", "g", "b"]
                },
                "brightness": {
                    "type": "integer",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "Brightness percentage (0-100)",
                    "default": 100
                }
            },
            "required": ["name", "color"]
        }
    }
]

# Function name to function mapping
LIGHT_FUNCTION_MAP = {
    "turn_on": turn_on,
    "turn_off": turn_off,
    "set_color": set_color
}

def execute_light_function(function_name: str, **kwargs) -> Dict[str, Any]:
    """
    Execute a light control function by name.

    Args:
        function_name: Name of the function to execute
        **kwargs: Function arguments

    Returns:
        Dictionary with result or error information
    """
    if function_name not in LIGHT_FUNCTION_MAP:
        return {"error": f"Unknown light function: {function_name}"}

    try:
        func = LIGHT_FUNCTION_MAP[function_name]
        return func(**kwargs)
    except Exception as e:
        logger.error(f"Error executing light function {function_name}: {e}")
        return {"error": f"Failed to execute {function_name}: {e}"}

def is_light_related_command(text: str) -> bool:
    """
    Check if a text command is related to light control.

    Args:
        text: The text to analyze

    Returns:
        True if the text appears to be a light control command
    """
    light_keywords = [
        'light', 'lights', 'bulb', 'bulbs', 'lamp', 'lamps',
        'turn on', 'turn off', 'switch on', 'switch off',
        'brighten', 'dim', 'brightness', 'color', 'colour',
        'red', 'green', 'blue', 'yellow', 'purple', 'pink',
        'white', 'warm', 'cool', 'lifx'
    ]

    text_lower = text.lower()
    return any(keyword in text_lower for keyword in light_keywords)
